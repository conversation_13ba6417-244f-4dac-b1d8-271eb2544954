/* Home Page Styles */

/* Hero Section */
.hero-section {
  width: 100%;
  overflow: hidden;
  margin-bottom: 2rem;
}

.hero-image {
  width: 100%;
  height: auto;
  display: block;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Section Titles */
.section-title {
  font-family: 'Roboto Slab', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1.5rem;
  position: relative;
}

/* Events Section */
.events-section {
  margin-bottom: 3rem;
}

.events-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

/* Featured Event Card (Left Block) */
.event-card--featured {
  height: 450px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Small Events Grid (Right Block) */
.events-grid-small {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 1rem;
  height: 450px;
}

/* Small Event Cards */
.event-card--small {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Common Event Card Styles */
.event-card__image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.event-card__header {
  padding: 1rem;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
}

.event-card__title {
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.event-card__date {
  color: #f0f0f0;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.event-card__description-box {
  background-color: #99002e; /* St. Paul's burgundy red */
  padding: 1rem;
  height: 25%;
  display: flex;
  align-items: center;
}

.event-card__description {
  color: white;
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;
}

/* Calendar Section */
.calendar-section {
  margin-bottom: 3rem;
}

.calendar-download-box {
  display: block;
  background-color: #99002e; /* St. Paul's burgundy red */
  border-radius: 8px;
  padding: 1.25rem;
  text-decoration: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease;
}

.calendar-download-box:hover {
  background-color: #7a0025; /* Darker red on hover */
}

.calendar-download-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-icon-wrapper {
  display: flex;
  align-items: center;
}

.calendar-icon {
  width: 32px;
  height: 32px;
  margin-right: 1rem;
}

.calendar-title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.download-icon-wrapper {
  display: flex;
  align-items: center;
}

.download-icon {
  width: 24px;
  height: 24px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
  }
  
  .event-card--featured {
    height: 350px;
  }
  
  .events-grid-small {
    height: auto;
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 200px);
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.5rem;
  }
  
  .event-card--featured {
    height: 300px;
  }
  
  .event-card__title {
    font-size: 1rem;
  }
  
  .calendar-title {
    font-size: 1rem;
  }
}