const assignmentModel = require('../models/assignmentModel');
const studentModel = require('../models/studentModel');
const subjectModel = require('../models/subjectModel'); // Adicionar importação
const renderAssignments = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    
    // Obter o studentId da query string se existir e não for vazio
    // Caso contrário, usar null para indicar "todos os alunos"
    const studentId = req.query.studentId && req.query.studentId !== "" 
                     ? parseInt(req.query.studentId, 10) 
                     : null;

    console.log('Usuário logado (ID):', userId);
    console.log('Função:', userRole);
    console.log('Aluno selecionado (query):', req.query.studentId);
    console.log('Aluno usado para filtro (após conversão):', studentId);

    let assignments = [];
    let students = [];

    // Administradores podem ver todas as tarefas ou filtrar por aluno
    if (userRole === 'admin') {
      // Usando a função correta do studentModel
      students = await studentModel.getAll();
      
      if (studentId) {
        console.log('Buscando tarefas para o aluno ID:', studentId);
        // Usando a função correta do assignmentModel (getByStudentId em vez de findAssignmentsByStudentId)
        assignments = await assignmentModel.getByStudentId(studentId);
      } else {
        console.log('Buscando todas as tarefas (sem filtro)');
        // Precisamos implementar uma função para buscar todas as tarefas
        // Vamos usar uma consulta temporária se a função não existir
        try {
          assignments = await assignmentModel.getAll();
        } catch (e) {
          console.log('Função getAll não encontrada, tentando consulta direta');
          // Implementação temporária se getAll não existir
          const db = require('../config/db');
          const result = await db.query(`
            SELECT a.*, s.Name as subject_name
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.subject_ID
            ORDER BY a.due_date
          `);
          assignments = result.rows;
        }
      }
      
      console.log('Tarefas encontradas:', assignments.length);
    } 
    // Pais só podem ver tarefas dos seus filhos
    else if (userRole === 'parent') {
      // Para pais, sempre usamos o studentId da sessão
      const parentStudentId = req.session.studentId ? parseInt(req.session.studentId, 10) : null;
      if (parentStudentId) {
        // Usando a função correta do assignmentModel
        assignments = await assignmentModel.getByStudentId(parentStudentId);
      }
    }

    res.render('pages/assignments', {
      assignments,
      students,
      user: userName,
      role: userRole,
      title: 'Tarefas',
      studentId: studentId // Passa o studentId para a view
    });

  } catch (error) {
    console.error('Erro ao carregar tarefas:', error);
    res.status(500).send('Erro ao carregar tarefas.');
  }
};
const renderAssignmentForm = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    let assignment = {
      School_ID: '', 
      subject_id: '', 
      description: '', 
      due_date: '', 
      Teams_link: '' 
    };
    
    const assignmentId = req.params.id;
    // Usando as funções corretas dos modelos
    const students = await studentModel.getAll();
    const subjects = await subjectModel.getAll(); // Ajuste conforme necessário
    
    if (assignmentId) {
      // Usando a função correta do assignmentModel (getById em vez de findAssignmentById)
      assignment = await assignmentModel.getById(assignmentId);
      if (!assignment) {
        return res.status(404).send('Tarefa não encontrada');
      }
    }
    
    res.render('pages/assignmentForm', {
      assignment,
      students,
      subjects,
      user: userName,
      role: userRole,
      title: assignmentId ? 'Editar Tarefa' : 'Adicionar Tarefa',
      isEdit: !!assignmentId
    });
    
  } catch (error) {
    console.error('Erro ao carregar formulário de tarefa:', error);
    res.status(500).send('Erro ao carregar formulário de tarefa.');
  }
};
const createOrUpdateAssignment = async (req, res) => {
  try {
    const userRole = req.session.userRole;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    const assignmentId = req.params.id;
    const { School_ID, subject_id, description, due_date, Teams_link, completed } = req.body;
    
    // Converter o valor de completed para booleano
    const isCompleted = completed === 'true';
    
    const assignmentData = { 
      School_ID,
      subject_id, 
      description, 
      due_date, 
      Teams_link,
      completed: isCompleted
    };
    
    if (assignmentId) {
      // Atualizar tarefa existente
      await assignmentModel.update(assignmentId, assignmentData);
    } else {
      // Criar nova tarefa
      await assignmentModel.create(assignmentData);
    }
    
    res.redirect('/assignments');
    
  } catch (error) {
    console.error('Erro ao salvar tarefa:', error);
    res.status(500).send('Erro ao salvar tarefa.');
  }
};const deleteAssignment = async (req, res) => {
  try {
    const userRole = req.session.userRole;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    const assignmentId = req.params.id;
    
    // Usando a função correta do assignmentModel (delete em vez de deleteAssignment)
    await assignmentModel.delete(assignmentId);
    
    res.redirect('/assignments');
    
  } catch (error) {
    console.error('Erro ao excluir tarefa:', error);
    res.status(500).send('Erro ao excluir tarefa.');
  }
};
const toggleCompleted = async (req, res) => {
  try {
    const userRole = req.session.userRole;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    const assignmentId = req.params.id;
    
    // Usando a função toggleCompleted do assignmentModel
    await assignmentModel.toggleCompleted(assignmentId);
    
    res.redirect('/assignments');
    
  } catch (error) {
    console.error('Erro ao alternar status da tarefa:', error);
    res.status(500).send('Erro ao alternar status da tarefa.');
  }
};

module.exports = {
  renderAssignments,
  renderAssignmentForm,
  createOrUpdateAssignment,
  deleteAssignment,
  toggleCompleted
};
