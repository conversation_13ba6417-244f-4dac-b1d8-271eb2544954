const assignmentModel = require('../models/assignmentModel');
const studentModel = require('../models/studentModel');
const subjectModel = require('../models/subjectModel'); // Adicionar importação
const renderAssignments = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    
    // Obter o studentId da query string se existir e não for vazio
    // Caso contrário, usar null para indicar "todos os alunos"
    const studentId = req.query.studentId && req.query.studentId !== "" 
                     ? parseInt(req.query.studentId, 10) 
                     : null;

    console.log('Usuário logado (ID):', userId);
    console.log('Função:', userRole);
    console.log('Aluno selecionado (query):', req.query.studentId);
    console.log('Aluno usado para filtro (após conversão):', studentId);

    let assignments = [];
    let students = [];

    // Administradores podem ver todas as tarefas ou filtrar por aluno
    if (userRole === 'admin') {
      students = await studentModel.findAllStudents();
      
      if (studentId) {
        console.log('Buscando tarefas para o aluno ID:', studentId);
        assignments = await assignmentModel.findAssignmentsByStudentId(studentId);
      } else {
        console.log('Buscando todas as tarefas (sem filtro)');
        assignments = await assignmentModel.findAllAssignments();
      }
      
      console.log('Tarefas encontradas:', assignments.length);
    } 
    // Pais só podem ver tarefas dos seus filhos
    else if (userRole === 'parent') {
      // Para pais, sempre usamos o studentId da sessão
      const parentStudentId = req.session.studentId ? parseInt(req.session.studentId, 10) : null;
      if (parentStudentId) {
        assignments = await assignmentModel.findAssignmentsByStudentId(parentStudentId);
      }
    }

    res.render('pages/assignments', {
      assignments,
      students,
      user: userName,
      role: userRole,
      title: 'Tarefas',
      studentId: studentId // Passa o studentId para a view
    });

  } catch (error) {
    console.error('Erro ao carregar tarefas:', error);
    res.status(500).send('Erro ao carregar tarefas.');
  }
};
const renderAssignmentForm = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    let assignment = {
      School_ID: '', 
      subject_id: '', 
      description: '', 
      due_date: '', 
      Teams_link: '' 
    };
    
    const assignmentId = req.params.id;
    const students = await studentModel.findAllStudents();
    const subjects = await subjectModel.findAllSubjects(); // Buscar todas as disciplinas
    
    if (assignmentId) {
      assignment = await assignmentModel.findAssignmentById(assignmentId);
      if (!assignment) {
        return res.status(404).send('Tarefa não encontrada');
      }
    }
    
    res.render('pages/assignmentForm', {
      assignment,
      students,
      subjects, // Passar as disciplinas para a view
      user: userName,
      role: userRole,
      title: assignmentId ? 'Editar Tarefa' : 'Adicionar Tarefa',
      isEdit: !!assignmentId
    });
    
  } catch (error) {
    console.error('Erro ao carregar formulário de tarefa:', error);
    res.status(500).send('Erro ao carregar formulário de tarefa.');
  }
};
const createOrUpdateAssignment = async (req, res) => {
  try {
    const userRole = req.session.userRole;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    const assignmentId = req.params.id;
    const { School_ID, subject_id, description, due_date, Teams_link } = req.body;
    
    const assignmentData = { 
      School_ID,
      subject_id, 
      description, 
      due_date, 
      Teams_link 
    };
    
    if (assignmentId) {
      // Atualizar tarefa existente
      await assignmentModel.updateAssignment(assignmentId, assignmentData);
    } else {
      // Criar nova tarefa
      await assignmentModel.createAssignment(assignmentData);
    }
    
    res.redirect('/assignments');
    
  } catch (error) {
    console.error('Erro ao salvar tarefa:', error);
    res.status(500).send('Erro ao salvar tarefa.');
  }
};const deleteAssignment = async (req, res) => {
  try {    const userRole = req.session.userRole;
        // Verificar se é administrador
    if (userRole !== 'admin') {      return res.status(403).send('Acesso negado');
    }    
    const assignmentId = req.params.id;    
    await assignmentModel.deleteAssignment(assignmentId);    
    res.redirect('/assignments');    
  } catch (error) {    console.error('Erro ao excluir tarefa:', error);
    res.status(500).send('Erro ao excluir tarefa.');  }
};
module.exports = {  renderAssignments,
  renderAssignmentForm,  createOrUpdateAssignment,
  deleteAssignment
};