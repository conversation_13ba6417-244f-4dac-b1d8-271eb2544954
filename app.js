const express = require('express');
const app = express();
const session = require('express-session')
const expressLayouts = require('express-ejs-layouts');
const routes = require('./src/routes');
const path = require('path')
const methodOverride = require('method-override')
require('dotenv').config();

// uses public for css
app.use(express.static(path.join(__dirname, 'public')));

app.use(express.json());
app.use(express.urlencoded({ extended: true}));

app.use(express.static(path.join(__dirname, 'src/views/css')));

// if use EJS:
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'src', 'views'));
app.use(expressLayouts);
app.set('layout', 'layout/main');
app.use(methodOverride('_method'));

app.use(session({
  secret: '10484928405905',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // coloque `true` se estiver usando HTTPS
  }
}));

app.use('/css', express.static(path.join(__dirname, 'src','views', 'css')));

app.use('/', routes);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () =>
  console.log(`Servidor rodando em http://localhost:${PORT}`)
);
