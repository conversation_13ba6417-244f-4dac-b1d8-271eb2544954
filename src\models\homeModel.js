const db = require('../config/db');

const homeModel = {
    async getEventsByYear(yearId) {
        try {
            console.log('Getting events for year ID:', yearId);
            const result = await db.query(
                'SELECT * FROM events WHERE year_ID = $1 ORDER BY event_date, time',
                [yearId]
            );
            console.log('Found events by year:', result.rows.length);
            return result.rows;
        } catch (error) {
            console.error('Error fetching events by year:', error);
            throw error;
        }
    },

    async getAllEvents() {
        try {
            console.log('Getting all events');
            const result = await db.query(
                'SELECT * FROM events ORDER BY event_date, time'
            );
            console.log('Found all events:', result.rows.length);
            return result.rows;
        } catch (error) {
            console.error('Error fetching all events:', error);
            throw error;
        }
    },

    async insertEvent(title, description, eventDate, time, photo, yearId) {
        try {
            const result = await db.query(
                'INSERT INTO events (title, description, event_date, time, photo, year_ID) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
                [title, description, eventDate, time, photo, yearId]
            );
            return result.rows[0];
        } catch (error) {
            console.error('Error inserting event:', error);
            throw error;
        }
    },

    async updateEvent(eventId, title, description, eventDate, time, photo, yearId) {
        try {
            const result = await db.query(
                'UPDATE events SET title = $1, description = $2, event_date = $3, time = $4, photo = $5, year_ID = $6 WHERE event_ID = $7 RETURNING *',
                [title, description, eventDate, time, photo, yearId, eventId]
            );
            return result.rows[0];
        } catch (error) {
            console.error('Error updating event:', error);
            throw error;
        }
    },

    async deleteEvent(eventId) {
        try {
            const result = await db.query(
                'DELETE FROM events WHERE event_ID = $1 RETURNING *',
                [eventId]
            );
            return result.rows[0];
        } catch (error) {
            console.error('Error deleting event:', error);
            throw error;
        }
    }
};

module.exports = homeModel;
