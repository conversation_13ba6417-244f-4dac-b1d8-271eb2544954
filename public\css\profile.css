/* Profile Page Styles */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto Slab', serif;
  background-color: #ffffff;
  color: #000000;
  line-height: 1.6;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info a {
  color: white;
  text-decoration: none;
  font-weight: 500;
}

.user-info a:hover {
  color: #820021;
}

/* Main Content */
.profile-content {
  min-height: calc(100vh - 120px);
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Section Titles */
.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #001D31;
  margin-bottom: 2rem;
  text-align: left;
}

/* Parent Information Section */
.parent-info-section {
  margin-bottom: 3rem;
  width: 100%;
}

.parent-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 250px;
  gap: 1.5rem;
  margin-bottom: 2rem;
  align-items: center;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 1rem;
  font-weight: 600;
  color: #001D31;
  margin-bottom: 0.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  font-size: 1rem;
  border: 2px solid #E1E1E1;
  border-radius: 8px;
  background-color: #ffffff;
  font-family: 'Roboto Slab', serif;
  transition: border-color 0.2s ease;
}

.input-wrapper input:focus {
  outline: none;
  border-color: #820021;
}

.edit-icon {
  position: absolute;
  right: 0.75rem;
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.edit-icon:hover {
  opacity: 1;
}

/* Profile Photo Column */
.profile-photo-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.profile-photo-wrapper {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid #E1E1E1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.profile-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-photo-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 0.875rem;
}

/* Photo Upload Functionality */
.photo-edit-overlay {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.photo-upload-input {
  display: none;
}

.photo-edit-button {
  display: block;
  cursor: pointer;
}

.profile-edit-icon {
  background-color: white;
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.profile-edit-icon:hover {
  transform: scale(1.1);
}

/* Student Photo Upload */
.student-photo-wrapper {
  position: relative;
}

.student-photo-edit-overlay {
  position: absolute;
  bottom: 5px;
  right: 5px;
}

.student-photo-edit-icon {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: brightness(0) invert(0);
  transition: transform 0.2s ease;
}

.student-photo-edit-icon:hover {
  transform: scale(1.1);
}

.hidden {
  display: none;
}

/* Save Button */
.save-button-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.btn-save {
  background-color: #820021;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Roboto Slab', serif;
  transition: background-color 0.2s ease;
  min-width: 120px;
}

.btn-save:hover {
  background-color: #99002e;
}

/* Student Information Section */
.student-info-section {
  margin-top: 3rem;
}

.student-medical-box {
  background-color: #820021;
  border-radius: 12px;
  padding: 1.5rem;
  color: white;
  position: relative;
  min-height: 200px;
}

.student-layout {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0rem;
}

.student-photo-section {
  flex-shrink: 0;
}

.student-photo {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
  border: 3px solid white;
}

.student-photo-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  color: white;
  font-size: 0.875rem;
  text-align: center;
}

.student-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.student-name-section {
  margin-bottom: 0.5rem;
}

.student-name-and-input {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1rem;
}

.student-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
}

/* Medical Input Section */
.medical-input-wrapper {
  position: relative;
  flex: 1;
}

.medical-input-wrapper textarea {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  font-size: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  font-family: 'Roboto Slab', serif;
  resize: vertical;
  min-height: 80px;
  box-sizing: border-box;
}

.medical-input-wrapper textarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.medical-input-wrapper textarea:focus {
  outline: none;
  border-color: white;
  background-color: rgba(255, 255, 255, 0.15);
}

.medical-edit-icon {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.medical-edit-icon:hover {
  opacity: 1;
}

/* Student Save Section */
.student-save-section {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
}

.student-save {
  background-color: #001D31;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Roboto Slab', serif;
  transition: all 0.2s ease;
}

.student-save:hover {
  background-color: #003366;
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.alert-info {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .parent-info-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .profile-photo-column {
    grid-column: 1 / -1;
    justify-content: center;
    margin-top: 1rem;
  }

  .profile-photo-wrapper {
    width: 150px;
    height: 150px;
  }

  .student-layout {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    flex-wrap: wrap;
  }

  .student-photo {
    width: 100px;
    height: 100px;
  }

  .student-photo-placeholder {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-content nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .parent-info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .profile-photo-wrapper {
    width: 120px;
    height: 120px;
  }

  .section-title {
    font-size: 1.5rem;
    text-align: center;
  }

  .student-layout {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .student-photo {
    width: 100px;
    height: 100px;
  }

  .student-photo-placeholder {
    width: 100px;
    height: 100px;
  }

  .student-save-section {
    position: static;
    text-align: center;
    margin-top: 1rem;
  }
}

@media (max-width: 480px) {
  .profile-content {
    padding: 1rem 0;
  }

  .container {
    padding: 0 0.75rem;
  }

  .parent-info-section,
  .student-info-section {
    margin-bottom: 2rem;
  }

  .form-group {
    gap: 1rem;
  }

  .input-wrapper input {
    padding: 0.625rem 2rem 0.625rem 0.625rem;
    font-size: 0.875rem;
  }

  .student-medical-box {
    padding: 1rem;
  }

  .student-name {
    font-size: 1.25rem;
  }

  .medical-input-wrapper textarea {
    font-size: 0.875rem;
    min-height: 60px;
    padding: 0.5rem 2rem 0.5rem 0.5rem;
  }

  .btn-save {
    padding: 0.625rem 1.5rem;
    font-size: 0.875rem;
  }

  .student-save {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  .student-photo {
    width: 80px;
    height: 80px;
  }

  .student-photo-placeholder {
    width: 80px;
    height: 80px;
    font-size: 0.75rem;
  }

  .medical-edit-icon {
    width: 14px;
    height: 14px;
    bottom: 0.25rem;
    right: 0.25rem;
  }
}