const db = require("../config/db");
const bcrypt = require("bcrypt");

const usersModel = {
  async findInParentByEmail(email) {
    const result = await db.query(
      "SELECT *, person_id AS id FROM parents WHERE email = $1",
      [email]
    );
    return result.rows[0];
  },

  async findInAdminByEmail(email) {
    const result = await db.query(
      "SELECT *, admin_id AS id FROM administrators WHERE email = $1",
      [email]
    );
    return result.rows[0];
  },

  async findAnyUserByEmail(email) {
    const parent = await this.findInParentByEmail(email);
    if (parent) {
      return { ...parent, role: "parent" };
    }
    const admin = await this.findInAdminByEmail(email);
    if (admin) {
      return { ...admin, role: "admin" };
    }
    return null;
  },

  async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }
};

module.exports = usersModel;
