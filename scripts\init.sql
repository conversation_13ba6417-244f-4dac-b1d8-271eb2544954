-- scripts/init.sql

-- 1) Tabela base: pais
CREATE TABLE parents (
  person_ID    SERIAL PRIMARY KEY,
  first_name   VARCHAR(100) NOT NULL,
  last_name    VARCHAR(100) NOT NULL,
  mobile       VARCHAR(20),
  email        VARCHAR(100),
  adress_line  TEXT,
  post_code    VARCHAR(20),
  photo        TEXT,
  password     VARCHAR(255) NOT NULL
);

-- 2) Professores
CREATE TABLE teacher (
  teacher_ID SERIAL PRIMARY KEY,
  first_name VARCHAR(100) NOT NULL,
  last_name  VARCHAR(100) NOT NULL,
  email      VARCHAR(100)
);

-- 3) Disciplinas
CREATE TABLE subjects (
  Subject_ID SERIAL PRIMARY KEY,
  Name       VARCHAR(100) NOT NULL,
  Code       VARCHAR(50)
);

-- 4) Classes vinculadas a ano, disciplina e professor
CREATE TABLE classes (
  class_ID   SERIAL PRIMARY KEY,
  year_code  VARCHAR(50), -- <PERSON> vir<PERSON> depois, pois ainda não existe `year`
  SubjectID  INT     REFERENCES subjects(Subject_ID),
  teacher_ID INT     REFERENCES teacher(teacher_ID)
);

-- 5) <PERSON><PERSON><PERSON>
CREATE TABLE students (
  School_ID      SERIAL PRIMARY KEY,
  person_ID      INT     NOT NULL REFERENCES parents(person_ID) ON DELETE CASCADE,
  class_ID       INT     REFERENCES classes(class_ID) ON DELETE SET NULL,
  first_name     VARCHAR(100) NOT NULL,
  last_name      VARCHAR(100) NOT NULL,
  year_code      VARCHAR(50),
  form_tutor     VARCHAR(100),
  photo          TEXT,
  Notifications  TEXT,
  house          VARCHAR(100)
);

-- 6) Anos escolares (agora pode referenciar `students`)
CREATE TABLE year (
  year_ID    SERIAL PRIMARY KEY,
  year_code  VARCHAR(50) UNIQUE NOT NULL,
  school_ID  INT     NOT NULL
                   REFERENCES students(School_ID)
                   ON UPDATE CASCADE
                   ON DELETE CASCADE
);

-- 7) Grade de aulas
CREATE TABLE timetable (
  Timetable_ID    SERIAL PRIMARY KEY,
  School_ID       INT     REFERENCES students(School_ID) ON DELETE CASCADE,
  Week_day        VARCHAR(20),
  start_time      TIME,
  end_time        TIME,
  room            VARCHAR(50),
  Timetable_cycle VARCHAR(50),
  class_ID        INT     REFERENCES classes(class_ID) ON DELETE SET NULL
);

-- 8) Eventos
CREATE TABLE events (
  event_ID    SERIAL PRIMARY KEY,
  title       VARCHAR(200) NOT NULL,
  description TEXT,
  event_date  DATE,
  time        TIME,
  photo       TEXT,
  year_ID     INT     REFERENCES year(year_ID) ON DELETE RESTRICT
);

-- 9) Relatórios
CREATE TABLE reports (
  report_ID    SERIAL PRIMARY KEY,
  School_ID    INT     REFERENCES students(School_ID) ON DELETE CASCADE,
  Term         VARCHAR(50),
  School_year  VARCHAR(20),
  pdf_path     TEXT
);

-- 10) Informações médicas
CREATE TABLE medical_info (
  School_ID       INT  PRIMARY KEY REFERENCES students(School_ID) ON DELETE CASCADE,
  medical_notes   TEXT,
  medical_contact TEXT
);

-- 11) Tarefas / assignments
CREATE TABLE assignments (
  assignmentID SERIAL PRIMARY KEY,
  School_ID    INT     REFERENCES students(School_ID) ON DELETE CASCADE,
  subject_id   INT     REFERENCES subjects(Subject_ID) ON DELETE CASCADE,
  description  TEXT,
  due_date     DATE,
  Teams_link   TEXT
);

-- 12) Administradores
CREATE TABLE administrators (
  admin_ID    SERIAL PRIMARY KEY,
  first_name  VARCHAR(100) NOT NULL,
  last_name   VARCHAR(100) NOT NULL,
  email       VARCHAR(100) UNIQUE NOT NULL,
  password    VARCHAR(255) NOT NULL
);
