-- scripts/init.sql

-- 1) Tabela base: pais
CREATE TABLE parents (
  person_ID    SERIAL PRIMARY KEY,
  first_name   VARCHAR(100) NOT NULL,
  last_name    VARCHAR(100) NOT NULL,
  mobile       VARCHAR(20),
  email        VARCHAR(100),
  adress_line  TEXT,
  post_code    VARCHAR(20),
  photo        TEXT,
  password     VARCHAR(255) NOT NULL
);

-- 2) Anos escolares 
CREATE TABLE year (
  year_ID    SERIAL PRIMARY KEY,
  year_code  VARCHAR(50)
);

-- 3) Disciplinas
CREATE TABLE subjects (
  subject_id SERIAL PRIMARY KEY,
  Name       VARCHAR(100) NOT NULL
);

-- 4) Professores
CREATE TABLE teacher (
  teacher_ID SERIAL PRIMARY KEY,
  first_name VARCHAR(100) NOT NULL,
  last_name  VARCHAR(100) NOT NULL,
  email      VARCHAR(100),
  photo      TEXT,
  subjectID  INT     REFERENCES subjects(subject_ID)
);

-- 5) Classes vinculadas a ano, disciplina e professor
CREATE TABLE classes (
  class_ID   SERIAL PRIMARY KEY,
  class-code TEXT,
  SubjectID  INT     REFERENCES subjects(subject_ID),
  teacher_ID INT     REFERENCES teacher(teacher_ID)
);

-- 6) Alunos
CREATE TABLE students (
  School_ID      SERIAL PRIMARY KEY,
  person_ID      INT     NOT NULL REFERENCES parents(person_ID) ON DELETE CASCADE,
  first_name     VARCHAR(100) NOT NULL,
  last_name      VARCHAR(100) NOT NULL,
  year_ID        INT     NOT NULL REFERENCES year(year_ID) ON DELETE RESTRICT,
  form_tutor     VARCHAR(100),
  photo          TEXT,
  notifications  BOOLEAN DEFAULT FALSE,
  house          VARCHAR(100)
);

-- 7) Grade de aulas
CREATE TABLE timetable (
  Timetable_ID    SERIAL PRIMARY KEY,
  School_ID       INT     REFERENCES students(School_ID) ON DELETE CASCADE,
  Week_day        VARCHAR(20),
  start_time      TIME,
  room            VARCHAR(50),
  timetable_cycle VARCHAR(50),
  class_ID        INT     REFERENCES classes(class_ID) ON DELETE SET NULL,
  teacher_ID      INT     REFERENCES teacher(teacher_ID) ON DELETE SET NULL,
  subject_ID      INT     REFERENCES subjects(Subject_ID) ON DELETE SET NULL
);

-- 8) Eventos
CREATE TABLE events (
  event_ID    SERIAL PRIMARY KEY,
  title       VARCHAR(200) NOT NULL,
  description TEXT,
  event_date  DATE,
  time        TIME,
  photo       TEXT,
  year_ID     INT     REFERENCES year(year_ID) ON DELETE RESTRICT
);

-- 9) Relatórios
CREATE TABLE reports (
  report_ID    SERIAL PRIMARY KEY,
  School_ID    INT     REFERENCES students(School_ID) ON DELETE CASCADE,
  term         VARCHAR(50),
  school_year  VARCHAR(20),
  pdf_path     TEXT
);

-- 10) Informações médicas
CREATE TABLE medical_info (
  School_ID       INT  PRIMARY KEY REFERENCES students(School_ID) ON DELETE CASCADE,
  medical_notes   TEXT,
  medical_contact TEXT
);

-- 11) Tarefas / assignments
CREATE TABLE assignments (
  assignmentID SERIAL PRIMARY KEY,
  School_ID    INT     REFERENCES students(School_ID) ON DELETE CASCADE,
  subject_id   INT     REFERENCES subjects(Subject_ID) ON DELETE CASCADE,
  description  TEXT,
  due_date     DATE,
  Teams_link   TEXT,
  completed    BOOLEAN DEFAULT FALSE
);

-- 12) Administradores
CREATE TABLE administrators (
  admin_ID    SERIAL PRIMARY KEY,
  first_name  VARCHAR(100) NOT NULL,
  last_name   VARCHAR(100) NOT NULL,
  email       VARCHAR(100) UNIQUE NOT NULL,
  password    VARCHAR(255) NOT NULL
);
