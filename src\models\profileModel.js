const db = require('../config/db');

const profileModel = {
  // Buscar informações do pai pelo ID
  getParentById: async (parentId) => {
    const query = 'SELECT * FROM parents WHERE person_ID = $1';
    const result = await db.query(query, [parentId]);
    return result.rows[0];
  },
  
  // Atualizar informações do pai
  updateParent: async (parentId, parentData) => {
    const query = `
      UPDATE parents 
      SET first_name = $1, last_name = $2, mobile = $3, 
          email = $4, adress_line = $5, post_code = $6, photo = $7
      WHERE person_ID = $8
      RETURNING *
    `;
    
    const values = [
      parentData.first_name,
      parentData.last_name,
      parentData.mobile,
      parentData.email,
      parentData.adress_line,
      parentData.post_code,
      parentData.photo,
      parentId
    ];
    
    const result = await db.query(query, values);
    return result.rows[0];
  },
  
  // Buscar informações médicas do estudante
  getMedicalInfo: async (studentId) => {
    const query = 'SELECT * FROM medical_info WHERE school_ID = $1';
    const result = await db.query(query, [studentId]);
    return result.rows[0];
  },
  
  // Atualizar informações médicas do estudante
  updateMedicalInfo: async (studentId, medicalData) => {
    // Verificar se já existe registro
    const checkQuery = 'SELECT * FROM medical_info WHERE school_ID = $1';
    const checkResult = await db.query(checkQuery, [studentId]);
    
    if (checkResult.rows.length > 0) {
      // Atualizar registro existente
      const query = `
        UPDATE medical_info 
        SET medical_notes = $1
        WHERE school_ID = $2
        RETURNING *
      `;
      const result = await db.query(query, [medicalData.medical_notes, studentId]);
      return result.rows[0];
    } else {
      // Criar novo registro
      const query = `
        INSERT INTO medical_info (school_ID, medical_notes)
        VALUES ($1, $2)
        RETURNING *
      `;
      const result = await db.query(query, [studentId, medicalData.medical_notes]);
      return result.rows[0];
    }
  }
};

module.exports = profileModel;
