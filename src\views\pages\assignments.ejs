<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tarefas</title>

  <!-- Link do CSS da página de tarefas -->
  <link rel="stylesheet" href="/css/assignments.css">

  <!-- (Opcional) Bootstrap ou outro CSS global -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>

<div class="container">
  <h2>Assignments</h2>
  
  <% if (role === 'admin') { %>
    <div class="admin-controls">
      <a href="/assignments/new" class="btn btn-primary">Adicionar Tarefa</a>
      
      <% if (students && students.length > 0) { %>
        <div class="student-filter">
          <form action="/assignments" method="GET" class="filter-form">
            <label for="studentFilter">Filtrar por aluno:</label>
            <select id="studentFilter" name="studentId">
              <option value="" <%= !studentId ? 'selected' : '' %>>Todos os alunos</option>
              <% students.forEach(student => { %>
                <option value="<%= student.school_id %>" <%= studentId && parseInt(studentId) === parseInt(student.school_id) ? 'selected' : '' %>>
                  <%= student.first_name %> <%= student.last_name %>
                </option>
              <% }) %>
            </select>
            <button type="submit" class="btn btn-sm btn-primary">Filtrar</button>
            <a href="/assignments" class="btn btn-sm btn-secondary">Limpar filtro</a>
          </form>
        </div>
      <% } %>
    </div>
  <% } %>

  <% const today = new Date(); %>

  <!-- Seção de Assigned -->
  <h3 class="section-title">Assigned</h3>
  <div class="assignments-list">
    <% const assignedTasks = assignments.filter(a => !a.completed); %>
    <% if (assignedTasks.length === 0) { %>
      <p>Nenhuma tarefa pendente.</p>
    <% } else { %>
      <% assignedTasks.forEach(assignment => { %>
        <div class="assignment-card">
          <div class="assignment-icon assignment"><img src="/images/assignedicon.png"></div>
          <div class="assignment-content">
            <p class="assignment-title"><%= assignment.description %></p>
            <p class="assignment-subject"><%= assignment.subject_name %></p>
            <p class="assignment-date">Due: <%= new Date(assignment.due_date).toLocaleDateString('pt-BR') %></p>

            <% if (assignment.Teams_link) { %>
              <p>
                <strong>Link do Teams:</strong> 
                <a href="<%= assignment.Teams_link %>" target="_blank">Acessar</a>
              </p>
            <% } %>

            <% if (role === 'admin') { %>
              <div class="admin-actions">
                <a href="/assignments/edit/<%= assignment.assignmentid %>" class="btn btn-sm btn-primary">Editar</a>
                <form action="/assignments/delete/<%= assignment.assignmentid %>" method="POST" class="delete-form">
                  <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir esta tarefa?')">Excluir</button>
                </form>
                <form action="/assignments/toggle-completed/<%= assignment.assignmentid %>" method="POST" class="toggle-form">
                  <button type="submit" class="btn btn-sm btn-success">Marcar como Concluída</button>
                </form>
              </div>
            <% } %>
          </div>
        </div>
      <% }) %>
    <% } %>
  </div>

  <!-- Seção de Completed -->
  <h3 class="section-title">Completed</h3>
  <div class="assignments-list">
    <% const completedTasks = assignments.filter(a => a.completed); %>
    <% if (completedTasks.length === 0) { %>
      <p>No completed tasks</p>
    <% } else { %>
      <% completedTasks.forEach(assignment => { %>
        <div class="assignment-card completed">
          <div class="assignment-icon completed"><img src="/images/completedicon.png"></div>
          <div class="assignment-content">
            <p class="assignment-title"><%= assignment.description %></p>
            <p class="assignment-subject"><%= assignment.subject_name %></p>
            <p class="assignment-date">Due: <%= new Date(assignment.due_date).toLocaleDateString('pt-BR') %></p>

            <% if (assignment.Teams_link) { %>
              <p>
                <strong>Link do Teams:</strong> 
                <a href="<%= assignment.Teams_link %>" target="_blank">Acessar</a>
              </p>
            <% } %>

            <% if (role === 'admin') { %>
              <div class="admin-actions">
                <a href="/assignments/edit/<%= assignment.assignmentid %>" class="btn btn-sm btn-primary">Editar</a>
                <form action="/assignments/delete/<%= assignment.assignmentid %>" method="POST" class="delete-form">
                  <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir esta tarefa?')">Excluir</button>
                </form>
                <form action="/assignments/toggle-completed/<%= assignment.assignmentid %>" method="POST" class="toggle-form">
                  <button type="submit" class="btn btn-sm btn-warning">Marcar como Pendente</button>
                </form>
              </div>
            <% } %>
          </div>
        </div>
      <% }) %>
    <% } %>
  </div>
</div>

</body>
</html>
