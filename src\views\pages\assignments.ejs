<div class="container">
  <h2>Tarefas</h2>
  
  <% if (role === 'admin') { %>
    <div class="admin-controls">
      <a href="/assignments/new" class="btn btn-primary">Adicionar <PERSON></a>
      
      <% if (students && students.length > 0) { %>
        <div class="student-filter">
          <form action="/assignments" method="GET" class="filter-form">
            <label for="studentFilter">Filtrar por aluno:</label>
            <select id="studentFilter" name="studentId">
              <option value="" <%= !studentId ? 'selected' : '' %>>Todos os alunos</option>
              <% students.forEach(student => { %>
                <option value="<%= student.school_id %>" <%= studentId && parseInt(studentId) === parseInt(student.school_id) ? 'selected' : '' %>>
                  <%= student.first_name %> <%= student.last_name %>
                </option>
              <% }) %>
            </select>
            <button type="submit" class="btn btn-sm btn-primary">Filtrar</button>
            <a href="/assignments" class="btn btn-sm btn-secondary">Limpar filtro</a>
          </form>
        </div>
      <% } %>
    </div>
  <% } %>
  
  <div class="assignments-list">
    <% if (assignments.length === 0) { %>
      <p>Nenhuma tarefa encontrada.</p>
    <% } else { %>
      <div class="assignments-grid">
        <% assignments.forEach(assignment => { %>
          <div class="assignment-card <%= new Date(assignment.due_date) < new Date() ? 'overdue' : '' %>">
            <h4><%= assignment.subject_name %></h4>
            
            <% if (assignment.student_name) { %>
              <p><strong>Aluno:</strong> <%= assignment.student_name %></p>
            <% } %>
            
            <p><strong>Data de entrega:</strong> <%= new Date(assignment.due_date).toLocaleDateString('pt-BR') %></p>
            
            <% if (assignment.description) { %>
              <p><strong>Descrição:</strong> <%= assignment.description %></p>
            <% } %>
            
            <% if (assignment.Teams_link) { %>
              <p>
                <strong>Link do Teams:</strong> 
                <a href="<%= assignment.Teams_link %>" target="_blank">Acessar</a>
              </p>
            <% } %>
            
            <% if (role === 'admin') { %>
              <div class="admin-actions">
                <a href="/assignments/edit/<%= assignment.assignmentid %>" class="btn btn-sm btn-primary">Editar</a>
                <form action="/assignments/delete/<%= assignment.assignmentid %>" method="POST" class="delete-form">
                  <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir esta tarefa?')">Excluir</button>
                </form>
              </div>
            <% } %>
          </div>
        <% }) %>
      </div>
    <% } %>
  </div>
</div>
