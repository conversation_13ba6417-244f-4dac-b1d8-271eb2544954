// src/controllers/reportsController.js
const reportModel = require('../models/reportModel');

exports.showReports = async (req, res, next) => {
  try {
    const studentId = req.session.studentId;
    if (!studentId) {
      return res.redirect('/about');
    }

    const years = await reportModel.findYearsByStudent(studentId);
    const selectedYear = req.query.year || null;
    let reports = [];

    if (selectedYear) {
      reports = await reportModel.findReportsByStudentYear(studentId, selectedYear);
    }

    res.render('pages/reports', {
      title: 'Reports',
      years,
      selectedYear,
      reports
    });
  } catch (err) {
    next(err);
  }
};
