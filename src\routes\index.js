const express = require("express");
const router = express.Router();
const studentsController = require("../controllers/studentsController");
const loginController = require("../controllers/loginController");
const timetableController = require("../controllers/timetableController");
const reportsController = require("../controllers/reportsController");
const homeController = require("../controllers/homeController");
const teachersController = require("../controllers/teachersController");
const profileController = require("../controllers/profileController");
const assignmentsController = require('../controllers/assignmentsController');

// Verificar se cada controlador e método existe antes de usar
// Login controller
router.get("/", loginController.renderLoginPage);
router.post("/", loginController.login);

// Student selection - corrigindo o nome do método
router.get("/about", studentsController.renderStudentSelectionPage);
router.post("/dashboard", studentsController.selectStudent);

// Timetable controller - verificar se os métodos existem
router.get("/timetable", timetableController.getAll || ((req, res) => res.send("Método não implementado")));
router.get("/timetable/:id", timetableController.getById || ((req, res) => res.send("Método não implementado")));
router.post("/timetable", timetableController.create || ((req, res) => res.send("Método não implementado")));
router.get("/timetable/:id/edit", timetableController.renderEditForm || ((req, res) => res.send("Método não implementado")));
router.post("/timetable/:id/edit", timetableController.update || ((req, res) => res.send("Método não implementado")));
router.post("/timetable/:id/delete", timetableController.delete || ((req, res) => res.send("Método não implementado")));

// Reports page
router.get("/reports", reportsController.showReports || ((req, res) => res.send("Método não implementado")));

// Teachers controller
router.get("/teachers", teachersController.renderTeacher || ((req, res) => res.send("Método não implementado")));
router.get("/teachers/new", teachersController.renderTeacherForm || ((req, res) => res.send("Método não implementado")));
router.get("/teachers/edit/:id", teachersController.renderTeacherForm || ((req, res) => res.send("Método não implementado")));
router.post("/teachers/save", teachersController.createOrUpdateTeacher || ((req, res) => res.send("Método não implementado")));
router.post("/teachers/save/:id", teachersController.createOrUpdateTeacher || ((req, res) => res.send("Método não implementado")));
router.post("/teachers/update-tutor", teachersController.updateFormTutor || ((req, res) => res.send("Método não implementado")));

// Home controller
router.get("/home", homeController.renderHomePage || ((req, res) => res.send("Método não implementado")));

// Profile controller
router.get("/profile", profileController.renderProfile || ((req, res) => res.send("Método não implementado")));
router.post("/profile/update", profileController.updateProfile || ((req, res) => res.send("Método não implementado")));

// Assignments controller
router.get('/assignments', assignmentsController.renderAssignments || ((req, res) => res.send("Método não implementado")));
router.get('/assignments/new', assignmentsController.renderAssignmentForm || ((req, res) => res.send("Método não implementado")));
router.get('/assignments/edit/:id', assignmentsController.renderAssignmentForm || ((req, res) => res.send("Método não implementado")));
router.post('/assignments/save', assignmentsController.createOrUpdateAssignment || ((req, res) => res.send("Método não implementado")));
router.post('/assignments/save/:id', assignmentsController.createOrUpdateAssignment || ((req, res) => res.send("Método não implementado")));
router.post('/assignments/delete/:id', assignmentsController.deleteAssignment || ((req, res) => res.send("Método não implementado")));
router.post('/assignments/toggle-completed/:id', assignmentsController.toggleCompleted || ((req, res) => res.send("Método não implementado")));

module.exports = router;
