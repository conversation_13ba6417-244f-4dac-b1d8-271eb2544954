const express = require("express");
const router = express.Router();
const studentsController = require("../controllers/studentsController");
const loginController = require("../controllers/loginController");
const timetableController = require("../controllers/timetableController");
const reportsController = require("../controllers/reportsController");
const homeController = require("../controllers/homeController");
const teachersController = require("../controllers/teachersController");
const profileController = require("../controllers/profileController");
const assignmentsController = require('../controllers/assignmentsController');

//Login controller
router.get("/", loginController.renderLoginPage);
router.post("/", loginController.login);

// Student selection
router.get("/about", studentsController.renderStudentSelection);
router.post("/dashboard", studentsController.selectStudent);

//Timetable controller
router.get("/timetable", timetableController.getAll); // renderiza a view
router.get("/timetable/:id", timetableController.getById);
router.post("/timetable", timetableController.create);
router.get("/timetable/:id/edit", timetableController.renderEditForm);
router.post("/timetable/:id/edit", timetableController.update);
router.post("/timetable/:id/delete", timetableController.delete);

// Reports page
router.get("/reports", reportsController.showReports);

// Teachers controller
router.get("/teachers", teachersController.renderTeacher);
router.get("/teachers/new", teachersController.renderTeacherForm);
router.get("/teachers/edit/:id", teachersController.renderTeacherForm);
router.post("/teachers/save", teachersController.createOrUpdateTeacher);
router.post("/teachers/save/:id", teachersController.createOrUpdateTeacher);
router.post("/teachers/update-tutor", teachersController.updateFormTutor);

// Home controller
router.get("/home", homeController.renderHomePage);

// Profile controller
router.get("/profile", profileController.renderProfile);
router.post("/profile/update", profileController.updateProfile);

//Assignments controllerAdd commentMore actions
router.get('/assignments', assignmentsController.renderAssignments);
router.get('/assignments/new', assignmentsController.renderAssignmentForm);
router.get('/assignments/edit/:id', assignmentsController.renderAssignmentForm);
router.post('/assignments/save', assignmentsController.createOrUpdateAssignment);
router.post('/assignments/save/:id', assignmentsController.createOrUpdateAssignment);
router.post('/assignments/delete/:id', assignmentsController.deleteAssignment);

module.exports = router;
