/* Home Page Styles */

/* Hero Section */
.hero-section {
  width: 100%;
  overflow: hidden;
  margin-bottom: 2rem;
}

.hero-image {
  width: 100%;
  height: auto;
  display: block;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Section Titles */
.section-title {
  font-family: 'Roboto Slab', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1.5rem;
  position: relative;
}

/* Events Section */
.events-section {
  margin-bottom: 3rem;
}

.events-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

/* Featured Event Card (Left Block) */
.event-card--featured {
  height: 450px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Small Events Grid (Right Block) */
.events-grid-small {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 1rem;
  height: 450px;
}

/* Small Event Cards */
.event-card--small {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Common Event Card Styles */
.event-card__image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.event-card__header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 70%, rgba(0,0,0,0) 100%);
  z-index: 2;
}

.event-card__title {
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  line-height: 1.2;
}

.event-card__date {
  color: #f0f0f0;
  font-size: 0.875rem;
  margin: 0.5rem 0 0 0;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.8);
  font-weight: 500;
}

.event-card__description-box {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #820021; /* Cor institucional */
  padding: 1rem;
  min-height: 25%;
  display: flex;
  align-items: center;
  z-index: 2;
}

.event-card__description {
  color: white;
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;
  font-weight: 400;
}

/* Specific styles for small event cards */
.event-card--small .event-card__title {
  font-size: 1rem;
  line-height: 1.1;
}

.event-card--small .event-card__date {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.event-card--small .event-card__description {
  font-size: 0.75rem;
  line-height: 1.3;
}

.event-card--small .event-card__header {
  padding: 0.75rem;
}

.event-card--small .event-card__description-box {
  padding: 0.75rem;
  min-height: 30%;
}

/* Calendar Section */
.calendar-section {
  margin-bottom: 3rem;
}

.calendar-download-box {
  display: block;
  background-color: #99002e; /* St. Paul's burgundy red */
  border-radius: 8px;
  padding: 1.25rem;
  text-decoration: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease;
}

.calendar-download-box:hover {
  background-color: #7a0025; /* Darker red on hover */
}

.calendar-download-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-icon-wrapper {
  display: flex;
  align-items: center;
}

.calendar-icon {
  width: 32px;
  height: 32px;
  margin-right: 1rem;
}

.calendar-title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.download-icon-wrapper {
  display: flex;
  align-items: center;
}

.download-icon {
  width: 24px;
  height: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .events-grid-small {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 200px);
    height: auto;
    gap: 1rem;
  }

  .event-card--featured {
    height: 350px;
  }

  .event-card--small {
    height: 200px;
  }

  .event-card__title {
    font-size: 1.1rem;
  }

  .event-card--small .event-card__title {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .event-card__header {
    padding: 0.75rem;
  }

  .event-card__description-box {
    padding: 0.75rem;
  }

  .event-card--small .event-card__header {
    padding: 0.5rem;
  }

  .event-card--small .event-card__description-box {
    padding: 0.5rem;
  }
}
