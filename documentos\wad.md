<img src="../assets/logointeli.png">


# WAD - Web Application Document - Módulo 2 - Inteli

## Royals

#### Names of group members

- [<PERSON><PERSON>](https://www.linkedin.com/in/adrianapolicia/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app)  
- [<PERSON>]()  
- [<PERSON>](https://www.linkedin.com/in/francisco-filho-87b243346/)  
- [<PERSON><PERSON><PERSON><PERSON>](https://www.linkedin.com/in/guilhermeholandamarques/)  
- [<PERSON>](https://www.linkedin.com/in/isaac-souza-santos/)  
- [<PERSON><PERSON>](https://www.linkedin.com/in/marcela-da-costa/)  
- [<PERSON>](https://www.linkedin.com/in/yan-de-olive<PERSON>-ribeiro-0a55a3356/)  

## Summary

[1. Introduction](#c1)

[2. Web Application Overview](#c2)

[3. Web Application Technical Project](#c3)

[4. Web Application Development](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](c#8)

[Anexos](#c9)

<br>


# <a name="c1"></a>1. Introduction (sprints 1 a 5)

St. Paul’s School, one of São Paulo's most traditional international schools, faces significant challenges in effectively communicating academic information to parents. Currently, limited access to school reports, class schedules, pending tasks, and other essential data is concentrated within a restrictive mobile app, leading to frustrating and impractical experiences for users.

To address this issue, we propose developing a web application or website—a centralized and accessible digital environment designed to provide a clear and fluid experience for student's parents. The portal will be accessible via desktop and mobile devices, offering quick login, consolidated student information, and intuitive navigation.

Key aspects of the product include the centralization and clarity of academic information, ease of use, and multilingual accessibility (English/Portuguese). Parents will be able to easily track simplified school reports, weekly schedules exportable to external tools (Google Calendar, Outlook), and receive personalized notifications about pending tasks and relevant school events. The portal will also facilitate direct updates to student's personal and medical information.

This solution aims to generate significant value for both the school and families, enhancing parental satisfaction by simplifying their academic monitoring routines and reducing the volume of direct inquiries to school administrative teams. By aligning the platform with specific user needs and maintaining an intuitive interface, this solution will further strengthen the relationship between St. Paul’s School and its school community.

## 1.1 Business Context — St Paul’s School
St Paul’s School is a 99-year-old British international school in São Paulo that commands high revenue in the premium K-12 sector and prides itself on tradition, academic excellence, innovation, ethics, and respect. The project’s aim is to launch a secure web portal that lets parents and guardians view grades, timetables, events, and other academic data in real time, thereby improving transparency and strengthening parent-school engagement. The student team provides programming, design, business, and leadership expertise, while the school supplies fictitious student data, domain knowledge, and staff access for testing and validation. St Paul’s fosters a collaborative, globally minded culture that resolves issues with a focus on educational outcomes, and lessons from prior coursework in stakeholder communication and user-centred design will guide development. Strict privacy rules prohibit any use of real personal information, so all data must remain anonymised. Success means universal adoption by parents and teachers, at least 80% parent satisfaction, and a measurable reduction in help-desk queries.

# <a name="c2"></a>2. Visão Geral da Aplicação Web

## 2.1. Escopo do Projeto (sprints 1 e 4)

### 2.1.1. Porter's 5 Forces Model

 &nbsp;&nbsp; Porter’s Five Forces is a strategic framework created by Michael Porter to assess the competitive intensity and, consequently, the economic attractiveness of an industry. Applied to education, it helps map the competitive landscape in which **St. Paul’s School** operates, highlighting the key challenges and forces shaping its market position.

<div align="center">

| Force | Level | Analysis |
| --- | --- | --- |
| **1. Rivalry among existing competitors** | **HIGH** | Despite its prestige and tradition, St. Paul’s faces strong competitors—Graded, Avenues, BCB, and St. Nicholas—which differentiate through innovation, diversity, and internationalisation. The premium education segment is highly competitive, with well-positioned schools and demanding families seeking more than traditional academic excellence. Continuous innovation is vital for St. Paul’s to retain leadership. |
| **2. Threat of new entrants** | **LOW** | The elite international-school market is saturated, dominated by established brands such as St. Paul’s, Graded, Avenues, and BCB. High entry barriers—significant capital outlay, international certifications, and long-term reputation building—make new school launches complex and slow. |
| **3. Bargaining power of suppliers** | **MODERATE-HIGH** | **International teachers – HIGH**: a global shortage of educators qualified for British/IB curricula drives up salaries and benefits. **Systems and platforms – LOW**: St. Paul’s can choose from several school ERPs; TOTVS, for instance, serves 8 of Brazil’s top 10 schools, diluting individual vendor power. Overall supplier power skews moderate-high due to dependence on specialised faculty. |
| **4. Bargaining power of customers** | **MODERATE** | Only a handful of schools in São Paulo match St. Paul’s calibre (Graded, Avenues, BCB, St. Nicholas). They all compete for affluent families with differentiated offerings, but within the centenary British sub-niche, St. Paul’s is virtually unique, tempering direct rivalry. Competitive pressure is notable yet not extreme. |
| **5. Threat of substitute products** | **LOW** | Substitutes have limited scale or pedagogical fit. Homeschooling serves ~35 k students nationwide and grows 36 % annually but faces regulatory hurdles and high parental involvement. Brazil’s 560+ EdTechs complement rather than replace premium in-person schooling. Mid-priced bilingual chains cannot match St. Paul’s brand equity, alumni network, or dual certification.
</div>

<div align="center">
  <img src="../assets/porters-five-forcers.png" width="80%">
</div>

### 2.1.2. SWOT Analysis of the Partner Institution
SWOT analysis is a strategic planning method used to evaluate the Strengths, Weaknesses, Opportunities, and Threats of a company, project, or organization. The main goal of this method is to gain a clear and realistic understanding of the business in order to support decision-making and plan for the future.

Below is the SWOT matrix of St. Paul's School, a renowned Brazilian school that aims to promote British culture, as well as an international teaching approach and a fantastic bicultural curriculum — all recognized by the United Kingdom itself. Therefore, in order to outline future plans and ensure the business is constantly evolving, it is essential to analyze the strengths — what makes St. Paul's School stand out; the weaknesses — areas for improvement; the opportunities — what can be better leveraged; and the threats — competitors targeting the same audience.

<div align="center">
  <sub>Analysis Swot</sub><br>
  <img src="../assets/analysis-swot.png" width="80%">
</div>

### 2.1.3. Solution (sprints 1 a 5)

1. Saint Paul’s School employs an app to help parents monitor their children’s progress, but the tool suffers from critical shortcomings. Not only does it restrict document access to mobile devices, but it also fails to show vital details like attendance records, timetables, and homework deadlines. These limitations leave parents frustrated, as they cannot access information fully or efficiently.

2. Not applicable
  
3. To address the current app's limitations, an online platform will be developed to centralise all academic information - attendance records, timetables, assignments, and grades - in a single dashboard. The intuitive interface will ensure parents can monitor their children's progress without technical difficulties.
   
4. The solution will be innovative with the creation of a web portal for parents. It will be accessed with encryption to prevent other people from having access to student data. The Portal will have a database containing: children's reports, absences, communications, calendars and tasks assigned centrally. This way we can guarantee parents' access to all children's information, but still protect their data.
   
5. The expected improvements are: making more information available to guardians about their children and better control over them, better communication and transparency in the school/guardian relationship, easier access to student data.
   
6. A successful result will be the use of the portal throughout the school, with 100% adoption by guardians and teachers. Success will be measured by user feedback, with at least 80% of guardians indicating that the platform helps them better monitor their children's academic progress, as well as improvements in tracking student's grades and attendance.

### 2.1.4. Value Proposition Canvas 

Value Proposition Canvas is a strategic tool businesses and designers use to analyze, design, evaluate and refine a product or service's value proposition to align with their customer's requirements. Businesses support their canvases with research, which informs product and service development, improvement and strategy, to visualize charts that break down into two main areas, which are: 

- Customer Profile - focuses on understanding the needs, challenges and aspirations. It is subdivided in three sections:
  - Customer Job(s): describes what customers are trying to get done, their objectives they want to impact their experience and performance.
  - Gains: describes the outcome customers want to achieve and the concrete benefits they desire to add value to the user experience. 
  - Pains: describes the bad outcomes, risks, obstacles and frustrations that prevent what the customer wants to achieve.

- Value Proposition - describes how your product or service resolves the clients needs. For that they divide in three sections: 
  - Products & Services: shows a list of solutions developed to attend the customers demands. 
  - Gain Creators: describe the features or benefits that create desired outcomes for the customers. 
  - Pain Relievers: describes how your products and services alleviate or eliminate customer's challenges and pains.

Below is the Value Proposition Canvas of St. Paul's School. This value proposition demonstrates how the initiative addresses employee's needs by alleviating challenges and creating real growth opportunities. Showing that this website positions itself as a tool to strengthen engagement, increase parent satisfaction, and enhance St. Paul's organizational performance.

<div align="center">
  <sub>Value Proposition Canvas</sub><br>
  <img src="../assets/Value Proposition.jpeg" width="80%">
</div>

### 2.1.5. Project Risk Matrix
<div align="center">
  <sub> Risk Matrix </sub><br>
<img width="665" alt="Matriz Final" src="https://github.com/user-attachments/assets/703d08b8-d242-48e8-9186-08f4ed65fc3c" />
</div>

#### *******. Solutions for the threats in the risk matrix

1. Teachers having difficulty with the new platform

 Solution: Provide training sessions to help teachers understand how to use the platform effectively, along with tutorials and technical support.

2. Users having access to other accounts

 Solution: Implement two-factor authentication (2FA) to enhance login security and prevent unauthorized access.
 
3. Student data leak

 Solution: Use encryption protocols to ensure student data is securely stored and transmitted.
 
4. Teacher feedback poorly formatted

 Solution: Standardize feedback formats using clear and easy-to-understand templates.
 
5. Low parental suitability due to bad UX

 Solution: Improve the platform’s design based on usability testing, ensuring it is intuitive and easy for parents to use.
 
6. Accessibility issues (students with special needs)

 Solution: Ensure the platform supports common accessibility standards (e.g., screen readers, keyboard navigation) and inclusive design practices.


## 2.2. Personas 

Personas are fictitious representations of user types that interact with the product. They are very important for us to better understand user needs and, thus, adapt the product to the target audience. In this regard, and based on an analysis of the characteristics described by the partners, three personas were created, each representing a type of user who interacts with the platform. In this section, we highlight the persona of a father, a mother, and a secretary from St. Paul's School, each with their individual stories and characteristics.



<div align="center">
  <sub>Persona 1</sub><br>
  <img src="../assets/persona1.png" width="85%"><br>
</div>

<div align="center">
  <sub>Persona 2</sub><br>
  <img src="../assets/persona2.png" width="85%"><br>
</div>

<div align="center">
  <sub>Persona 3</sub><br>
  <img src="../assets/persona3.png" width="85%"><br>
</div>

## 2.3. User Stories (sprints 1 a 5)

Identification | US01 
--- | ---
Persona | Paulo Ribeiro
User Story | "As a parent, I want to be able to log into the Parent Portal, so I can keep track of my child’s academic life."
Acceptance Criteria | CR1: A parent with a valid registration, should be able to do a login in the Parent Portal.
INVEST Criteria: | 
Independent: | the login ability is independent from the other parts, since it can be developed and tested separately.
Negotiable: | the details of how the login is made can be adjusted as the project evolves.
Valuable: | the login is essential so that each parent can be associated with their own child.
Estimable: | it's possible to estimate the time and effort needed to implement this function 
Small: | the creation of the login is a simple and small task, that can be developed easily.
Testable: | it's easy to test and check if the login is working during the development of the project. 

Identification | US02 
--- | ---
Persona | Paulo Ribeiro
User Story | "As a parent, I want to access a screen containing my child's information, so I can view it in a centralized way."
Acceptance Criteria | CR1: A parent with a registration linked to their child, should be able to view his child's academic information.  
INVEST Criteria: | 
Independent: | the first page including the child's information is independent from the other parts, since it can be developed and tested separately.
Negotiable: | the details of how the first page is made can be adjusted as the project evolves.
Valuable: | the child's data is essential so that each parent can check their child's information and be up to date with it. 
Estimable: | it's possible to estimate the time and effort needed to implement this function. 
Small: | the creation of the page with all the child's data is a simple and small task, that can be developed easily.
Testable: | it's easy to test and check if the first page is working during the development of the project.
 
Identification | US03 
--- | ---
Persona | Sarah Marques
User Story | "As a parent, I want to access a screen containing all my child's teachers' contact information, so I can reach out when needed and be informed about who gives his classes."
Acceptance Criteria | CR1: A parent with a registration linked to their child, should have access to the teachers responsible for the child's education.
INVEST Criteria: | 
Independent: | the screen with all the teacher's contacts and information is independent from the other parts, since it can be developed and tested separately.
Negotiable: | the details of how the screen with the teacher's information is made can be adjusted as the project evolves.
Valuable: | the teacher's information page is essential so that each parent can be in touch and well informed about the teacher's information.
Estimable: | it's possible to estimate the time and effort needed to implement this function.
Small: | the creation of the page with the teacher's information is a simple and small task that can be developed easily.
Testable: | it's easy to test and check if this screen is working during the development of the project.

Identification | US04
--- | ---
Persona | Sarah Marques
User Story | "As a parent of two children, I want to be able to select which child's academic information to view, displaying one at a time, so I can access both of their data with a single login, in an organized manner."
Acceptance Criteria | CR1: A parent with a registration linked to two or more children, should be able to select which child's information he wants to visualize. 
INVEST Criteria: | 
Independent: | the child selection screen is independent from the other parts, since it can be developed and tested separately.
Negotiable: | the details of how the screen with the child selection is made can be adjusted as the project evolves.
Valuable: | the childs selection page is essential so that each parent view and separate their childs information.
Estimable: | it's possible to estimate the time and effort needed to implement this function.
Small: | the creation of the childs selection page is a simple and small task that can be developed easily.
Testable: | it's easy to test and check if this screen is working during the development of the project.

Identification | US05
--- | ---
Persona | Paulo Ribeiro
User Story | "As a parent, I want to be able to check a screen with the lesson activities assigned to my child, so I can monitor, guide, and stay updated on his academic work."
Acceptance Criteria | CR1: A parent with a registration linked to their child should be able to view a list of assignments pulled from the Microsoft Teams API, including title, description, due date, and completion status.
INVEST Criteria: | 
Independent: | the screen with all the assignments is independent from the other parts, since it can be developed and tested separately.
Negotiable: | the details of how the screen with the assignments is made can be adjusted as the project evolves.
Valuable: | the assignments page is essential so that each parent can be well informed about their childs homework.
Estimable: | it's possible to estimate the time and effort needed to implement this function.
Small: | the creation of the assignments page is a simple and small task that can be developed easily.
Testable: | it's easy to test and check if this screen is working during the development of the project.

Identification | US06
--- | ---
Persona | Paulo Ribeiro
User Story | "As a parent, I want to view a screen with my child's timetable so I can check their class schedule and upcoming events to organize my calendar."
Acceptance Criteria | CR1: A parent with a registration linked to their child, should be able to view the child's timetable

Identification | US07
--- | ---
Persona | Sarah Marques
User Story | "As a parent, I want to view a screen with my child's grade reports and convert them into PDF format so I can stay informed and download them if needed."
Acceptance Criteria | CR1: A parent with a registration linked to their child, should be able access his child's report card.

Identification | US08
--- | ---
Persona | Paulo Ribeiro
User Story | "As a parent, I want to be able to edit my personal information, so I can keep St. Paul's School updated with my contact details."
Acceptance Criteria | CR1: A parent registered to the Parent Portal, should be able to edit their personal data.

Identification | US09
--- | ---
Persona | Sarah Marques
User Story | "As a parent, I want to be able to update my child’s medical data, so I can inform the school about health conditions in case of emergencies."
Acceptance Criteria | CR1: A parent with a registration linked to their child, should be able to edit his child's medical data.

Identification | US10
--- | ---
Persona | Roberto Carlos
User Story | "As a website manager, I want to have a page to add news for parents, so I can keep them well informed."
Acceptance Criteria | CR1: The website manager should be able to add news about school events in the Parent Portal.

Identification | US11
--- | ---
Persona | Roberto Carlos
User Story | "As a website manager, I want to be abke to add student's grades to the system, so I can keep parents updated on their child's progress."
Acceptance Criteria | CR1: The website manager should be able to add grades given by their teachers to each student.

Identification | US12
--- | ---
Persona | Roberto Carlos
User Story | "As responsible for the website, I want the project documentation to be organized and clear so that I can make updates more safely."
Acceptance Criteria | CR1: The Project documentation must be updated periodically, reflecting the structure and technical decisions adopted. <br> CR2: The documentation must be stored with version control (such as Git) to ensure history and traceability. <br> CR3: The organization of documentation should facilitate understanding by new contributors or administrators.

Identification | US13
--- | ---
Persona | Roberto Carlos
User Story | "As an administrator, I want to be able to update information in the database to keep data on parents, students and dynamic content (such as announcements and calendar) always up to date"
Acceptance Criteria | CR1: Allow editing of parent and student data directly through the administrative interface. <br> CR2: Allow the updating of dynamic content such as notices and calendars. <br> CR3: Changes must reflect correctly in the database upon commit.


# <a name="c3"></a>3. Projeto da Aplicação Web (sprints 1 a 4)

## 3.1. Architecture

## MVC Architecture Overview

Our Parents Portal is built using the **Model-View-Controller (MVC)** architecture, ensuring clear separation between data, logic, and presentation. This structure makes the system more organized, maintainable, and scalable.

<div align="center">
<br><img src="../assets/diagrama-mvc.png" width="85%"><br>
</div>



### Models

Each **Model** represents a table in the database and handles all direct data operations (CRUD). Below are the main models and their purposes:

- **`parents`** – Responsible for parent user information.
- **`teacher`** – Stores data about professors.
- **`subjects`** – Represents school subjects.
- **`classes`** – Associates year, subject, and teacher.
- **`students`** – Main student table with links to parents and classes.
- **`year`** – School year identifiers.
- **`timetable`** – Weekly schedule for students.
- **`events`** – School events and announcements.
- **`reports`** – Report card file metadata.
- **`medical_info`** – Health-related data for each student.
- **`assignments`** – Task list pulled or synced with Teams.
- **`administrators`** – Admin credentials for internal access.

---

### Controllers

**Controllers** handle the application's business logic. They receive input from the views, process it (including any model interaction), and return the result to the view.

| Controller             | Purpose |
|------------------------|---------|
| `loginController`      | Validates parent login via `parents` table. |
| `personInfoController` | Manages parent profile and student medical info (`SELECT`, `UPDATE`, `DELETE`). |
| `aboutController`      | Loads student list and general info for the “About” view. |
| `homeController`       | Handles `events` (`SELECT`, `UPDATE`, `DELETE`, `INSERT`) for dashboard display. |
| `assignmentsController`| Full CRUD for `assignments`, including status updates. |
| `timeTableController`  | CRUD operations on student `timetable` entries. |
| `reportsController`    | Handles listing, adding, and updating `reports`. |
| `teacherController`    | Manages data in the `teacher` table. |

---

### Views

**Views** are the user-facing screens. They communicate with controllers to display or update data.

- `Login` – Form to authenticate parent users.
- `Personal_Medical` – Includes:
  - Header with student selector.
  - Editable parent information list.
  - Student medical info section.
- `About` – Displays basic student profiles.
- `Home` – Includes:
  - Event list and calendar widget.
  - General welcome header and footer.
- `Assignments` – Shows assignments categorized by status.
- `Timetable` – Weekly schedule (Week 1 and Week 2).
- `Reports` – Displays list of downloadable student report cards.
- `Teacher` – Displays tutors and subject teachers assigned to the student.

## Class Diagram
<div align="center">
  <sub>Wireframe - User</sub><br>
  <img src="../assets/diagramaUml.png" width="85%"><br>
</div>

### 1. Model Layer (Data and Business Logic)

The Model layer in the MVC diagram is where business logic and data representation reside. The domain classes from the Class Diagram directly fit into this layer, being responsible for encapsulating data and the business rules related to it.

**Classes in the Model:**

* **Parent:** Represents parents/guardians. Contains attributes such as name, contact, address, and methods for login, viewing children's information, grades, etc.
* **Student:** Represents students. Contains attributes such as name, form tutor, photo, and methods for retrieving information related to themselves (class, year, medical, etc.).
* **Teacher:** Represents teachers. Contains identification and contact attributes, with methods for managing assignments and reports.
* **Administrator:** Represents system administrators. Contains identification attributes and methods for managing users and creating global content.
* **Year:** Represents academic years. Contains the year code and value.
* **Subject:** Represents academic subjects. Contains the subject's name and code.
* **Class:** Represents a class or group of students for a specific subject/year.
* **MedicalInfo:** Contains a student's medical information.
* **Assignment:** Represents a task or activity. Contains title, description, and due date.
* **TimetableEntry:** Represents a timetable/calendar entry (lesson, event). Contains day, time, and room details.
* **Event:** Represents school events. Contains the event's title, date, and time.
* **Report:** Represents student reports/grade sheets. Contains the term, academic year, and PDF path.

**Relationship with MVC:** These classes are instantiated and manipulated by the Controllers and persist their data in the Server (Database), as indicated in the MVC Diagram, where the "Models" section details the corresponding tables (e.g., `parents`, `students`, `medical_info`, etc.).

### 2. View Layer (User Interface)

The View layer in the MVC diagram is responsible for presenting information to the user. The domain classes (Model) provide the data that is displayed by the Views.

**Relationship with MVC:**
The "Views" (Login, Parents, Personal_Medical, About, Students list, Assignments, Timetable, Reports, Teacher) in the MVC Diagram directly consume the information provided by instances of the Model classes. For example:

* The `View: Parents` displays data from the logged-in Parent and their Students.
* The `View: Students list` displays data from multiple Students.
* The `View: Assignments` displays data from the `Assignment` class.
* The `View: Timetable` displays data from the `TimetableEntry` class.
* The `View: Reports` displays data from the `Report` class.

Views also capture user interactions (clicks, form submissions) and send them to the corresponding Controllers.

### 3. Controller Layer (Control/Coordination Logic)

The Controller layer in the MVC diagram acts as an intermediary, receiving requests from the Views, manipulating the Model classes, and deciding which View to display.

**Relationship with MVC:**
The "Controllers" in the MVC Diagram (LoginController, HomeController, AboutController, StudentController, AssignmentController, TimeTableController, ReportController, TeacherController) invoke methods and manipulate instances of the domain classes (Model). For example:

* `LoginController` uses the `login()` method of the `Parent` class (or `Teacher`, `Administrator`).
* `HomeController` and `StudentController` interact with instances of `Student`, `Parent`, `MedicalInfo`, `Year`, etc., to retrieve data or perform operations such as `updateProfile()`.
* `AssignmentController` interacts with the `Assignment` class to `uploadAssignment()` (if coming from the Teacher) or `viewAssignments()` (if coming from the Parent/Student).
* `Administrator` (as a Model class) would be orchestrated by an `AdminController` or by `HomeController` methods to perform operations such as `addUser()`, `createSubject()`, etc.

---


### Example Flow – Updating Medical Info

1. **Parent Action:** Clicks “Edit Medical Info.”
2. **View:** Triggers `personInfoController.update(...)`.
3. **Controller:** Processes data and calls `medical_info` model.
4. **Model:** Executes the SQL `UPDATE` query.
5. **Controller:** Optionally fetches updated info with `SELECT`.
6. **View:** Re-renders the component with the latest data.

---

### Benefits of Using MVC

- **Separation of concerns**: logic, data, and interface are independent.
- **Maintainability**: easy to debug and add new features.
- **Scalability**: future extensions (like an API or SPA frontend) are simplified.

## 3.2. Wireframes

Wireframes are simplified visual representations of a page or digital interface that show the basic structure and layout of elements such as buttons, menus, images, and text, without focusing on graphic or aesthetic details. They act as the skeleton of the design, helping to plan the organization and flow of information clearly and objectively. Below are images of the wireframe that will serve as the basis for the application's visual design:

<div align="center">
  <sub>Wireframe - Login and About</sub><br>
  <img src="../assets/wireframe1.png" width="80%"><br>
</div>

<div align="center">
  <sub>Wireframe - Home</sub><br>
  <img src="../assets/wireframe2.png" width="80%"><br>
</div>

<div align="center">
  <sub>Wireframe - Personal Information and Teachers</sub><br>
  <img src="../assets/wireframe3.png" width="80%"><br>
</div>

<div align="center">
  <sub>Wireframe - Timetable, Assigments and Reports</sub><br>
  <img src="../assets/wireframe4.png" width="80%"><br>
</div>

The user's flow in the portal starts with the login screen, followed by the selection of the student they want to track. On the homepage, the parent has access to school events and the academic calendar. From this screen, they can access features such as viewing and downloading report cards (Reports) in PDF, checking the class schedule (Timetable), following up on assigned tasks, and accessing the teachers' contact information. The user can also edit their own personal details and update the student’s medical information.

<div align="center">
  <sub>Wireframe - Login and Home Admin</sub><br>
  <img src="../assets/wireframe5.png" width="80%"><br>
</div>

<div align="center">
  <sub>Wireframe - Teacher and Reports Admin</sub><br>
  <img src="../assets/wireframe6.png" width="80%"><br>
</div>

The administrator's flow allows them to add events and update the school calendar directly from the portal’s homepage. Additionally, they can edit students’ report cards by filtering and selecting which student will receive the updates. The administrator can also modify the information of all teachers linked to a specific student, keeping the platform’s data up to date.

## 3.3. Style Guide

This style guide establishes the visual and functional guidelines of the web application, ensuring **aesthetic consistency, accessibility, and alignment with the institutional identity of St. Paul’s School**. It brings together the main visual elements used in the project, such as **typography, color palette, icons, illustrations, buttons, and logos**, providing standardization for the interface development process.

<div align="center">
  <sub>Style Guide</sub><br>
  <img src="../assets/style-guide.png" width="80%"><br>
</div>

By following this guide, we ensure an intuitive and easily recognizable visual experience, directly contributing to system usability, reinforcing the school’s institutional image, and ensuring a positive user experience.

### 3.3.1 Colors

The color palette was carefully selected to reflect the tradition and excellence of St. Paul’s School, maintaining a strong and respectable visual identity. The **primary colors** — burgundy and dark blue — are used in key elements such as buttons, headers, footers, and frames. The **secondary colors** — white, gray, and black — complement the layout, being applied to backgrounds, text boxes, and typography.

#### Color palette used:

| Color       | Hex Code   | Main Usage                                              |
|-------------|------------|----------------------------------------------------------|
| Burgundy    | `#820021`  | Primary color — buttons, highlights, and titles         |
| Dark Blue   | `#001D31`  | Primary color — header, footer, institutional text      |
| White       | `#FFFFFF`  | Secondary color — background and contrast               |
| Gray        | `#E1E1E1`  | Secondary color — borders, dividers, subtle elements    |
| Black       | `#000000`  | Secondary color — main text, icons                      |

These colors are applied consistently throughout the interface, ensuring contrast, readability, and a pleasant visual experience for users.

### 3.3.2 Typography

The solution uses the **Roboto Slab** font as the base for all textual content in the system, ensuring visual consistency, readability, and alignment with St. Paul’s School’s institutional identity.

#### Typography — Desktop Version

Below is the typographic hierarchy used in the desktop interface, including font weight, size, and primary function:

<div align="center">
  <sub>Typography — Desktop</sub><br>
  <img src="../assets/typography-desktop.png" width="40%"><br>
</div>

| Style            | Weight    | Size (px)    | Primary function                             |
|------------------|-----------|--------------|----------------------------------------------|
| **H1**           | Regular   | 40px         | Main titles and section headers              |
| **H2 Strong**    | SemiBold  | 36px         | Highlighted subtitles                        |
| **H2**           | Regular   | 36px         | Standard subtitles                           |
| **H3 Strong**    | SemiBold  | 32px         | Tertiary titles with emphasis                |
| **H3**           | Regular   | 32px         | Tertiary titles                              |
| **H4**           | Regular   | 24px         | Minor highlights and captions                |
| **p**            | Regular   | 20px         | Paragraphs and descriptive text              |
| **sub**          | Regular   | 15px         | Informational or supporting subtitles        |

#### Typography — Mobile Version

For mobile devices, the typography is adapted to maintain readability on smaller screens, using reduced font sizes while preserving the overall hierarchical structure:

<div align="center">
  <sub>Typography — Desktop</sub><br>
  <img src="../assets/typography-mobile.png" width="40%"><br>
</div>

| Style            | Weight    | Size (px)    | Primary function                             |
|------------------|-----------|--------------|----------------------------------------------|
| **H1 Strong**    | SemiBold  | 30px         | Main title with emphasis                     |
| **H1**           | Regular   | 30px         | Main title                                   |
| **H2 Strong**    | SemiBold  | 24px         | Highlighted subtitle                         |
| **H2**           | Regular   | 24px         | Standard subtitle                            |
| **H3**           | Regular   | 20px         | Third-level title                            |
| **H4 Strong**    | SemiBold  | 15px         | Minor highlight with emphasis                |
| **H4**           | Regular   | 15px         | Minor highlight                              |
| **H5**           | Regular   | 10px         | Auxiliary or secondary text                  |
| **sub**          | Regular   | 8px          | Supporting or complementary text             |
| **p**            | Regular   | 5px          | Footer or subtle informational text          |

### 3.3.3 Iconography and images 

### Icons

The icons used in the project aim to standardize and facilitate user navigation and to represent the platform’s functionalities. Each icon has a specific purpose—informative, illustrative, or institutional—and carries meaning to help users better understand the actions available on the site. The use of icons also makes the site more user-friendly and intuitive, contributing to a more pleasant user experience.

<div align="center">
  <sub>Icons</sub><br>
  <img src="../assets/icons.png" width="85%"><br>
</div>

**Icon descriptions:**

- **Reports button**: button that directs to available reports. *(Purpose: informative)*
- **Assignments button**: button that gives access to assigned tasks. *(Purpose: informative)*
- **Timetable button**: button to view the activity schedule. *(Purpose: informative)*
- **Teachers button**: button to access teacher information. *(Purpose: informative)*
- **Visual representation of a PDF file**: icon used to indicate PDF files. *(Purpose: illustrative)*
- **Visual representation of a notification alert**: visual alert for new notifications. *(Purpose: illustrative)*
- **Visual representation of a topic**: visual marker for available topics. *(Purpose: illustrative)*
- **Visual representation of the assigned assignments**: marker for assigned tasks. *(Purpose: illustrative)*
- **Visual representation of the completed assignments**: icon indicating completed tasks. *(Purpose: illustrative)*
- **Create button (admin)**: admin-only button to create new content or records. *(Purpose: functional)*
- **Delete button (admin)**: admin-only button to remove records. *(Purpose: functional)*
- **Edit button**: button used to edit existing information. *(Purpose: functional)*

### Illustrations

In addition to iconography, the platform also includes illustrative images to enhance the website design and improve user experience. Some images aim to make navigation more pleasant, while others are used to contextualize content or even inform users, such as the photos of the school’s teaching staff. Each image therefore plays a specific role within the website’s context and contributes significantly to the visual identity that conveys the school’s essence.

<div align="center">
  <sub>Illustrations</sub><br>
  <img src="../assets/illustrations.png" width="45%"><br>
  <a href="https://www.figma.com/design/ngNzHhdOiPz6ft2H43t6Ao/Untitled?node-id=131-608&p=f&t=HlQKegMmYaxBKClk-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

**Image descriptions:**

- **Images of classrooms and school activities**: used in presentation and landing pages. *(Purpose: illustrative and institutional)*
- **Photos of the school building and facilities**: help to create a familiar environment and reinforce the school’s visual identity. *(Purpose: institutional)*
- **Photos of students**: humanize the platform and make navigation more welcoming. *(Purpose: illustrative)*
- **Portraits of teachers and staff**: featured on administrative pages and the staff information section. *(Purpose: informative)*
- **St. Paul’s School logos**: used in headers and footers throughout the application. *(Purpose: institutional)*
- **Royals logo**: used in the footers throughout the application. *(Purpose: illustrative)*

## 3.4 High fidelity prototype

  ## Users Desktop
<div align="center">
  <sub>Login and Student Selection - User</sub><br>
  <img src="../assets/prototype1user.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=0-1&p=f&t=8VtQ5Ynbw4hI44c3-0">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 1 – Login (User) 
  The first screen is the login page for parents. We designed it to be simple and focused only on essential information, such as the email and password fields. However, to avoid making it look too plain and to ensure users recognize they are on the St. Paul’s School website, we added the school logo and a background image of the students. This helps give the page more personality and reinforces the school’s identity.

  ## Screen 2 – Student Selection (User) 
  After logging in, users are directed to the student selection screen. Here, we organized the key information of each student into interactive cards, which also act as buttons. Each card displays the student’s photo, name, academic year, and house, making it easy for parents to quickly identify their child. We also added a notification icon to alert parents visually whenever there is new information or updates on the platform. The school’s color palette is applied throughout the screen to maintain a strong visual identity and create a user-friendly experience.

<div align="center">
  <sub> Home - User</sub><br>
  <img src="../assets/prototype2user.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=0-1&p=f&t=8VtQ5Ynbw4hI44c3-0">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 3 and 4 – Home Page (User)
Once a student is selected, the user is directed to the home page. At the top, there is a header with a navigation menu containing:
- Change Student (to return to the student selection screen);
- Home (the current page);
- Academic Life, which has a dropdown menu with four options: Reports, Assignments, Timetable, and Teachers;
- Personal Information, where parents can access their personal details.

Below the header, we added a large image inspired by the current school website to give the page more character. Then, there is the Events section, which displays the main upcoming events with key details like title, date, time, and a short description. Each event card also includes a background image to make the information more visually engaging. On the right side, there is an arrow that allows users to scroll through additional events if there are more than fit on the screen.

Following the events section is the Calendar, where the school’s PDF calendar is available for download. A PDF icon and a download button make this feature clear and accessible.

Finally, we added a footer featuring the school’s logo and our logo to reinforce the brand identity consistently across all pages.

<div align="center">
  <sub> Personal Information and Teachers - User</sub><br>
  <img src="../assets/prototype3user.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=0-1&p=f&t=8VtQ5Ynbw4hI44c3-0">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 5 – Personal Information (User)
This screen allows parents to view and edit their personal information and their child’s medical details. The layout follows the same clean and organized structure as previous screens. and the use of the school's colors reinforces the brand identity and helps the user easily recognize important actions, like the red Save button. At the top, the header remains consistent with the previous screens, containing Change Student, Home, Academic Life, and Personal Information.

The first section, Parent Personal Information, includes editable fields such as full name, date of birth, address, email, phone number and photo. The presence of a profile picture adds a personal touch and improves the user’s connection with the platform. There's also an icon that represents an editable feature  to upload or change the profile photo and the personal data, ensuring the information is always up to date. The Save button is provided to confirm any changes made.

The second section, Student Medical Information, displays the selected student's profile along with a field to add or update important medical information, such as allergies or medical conditions. This section also includes a Save button, ensuring any updates are stored properly. The card style used maintains consistency with the selection screen, making the interface intuitive
  ## Screen 6 – Teachers (User)
This screen displays the student's teachers. At the top, the familiar header is present for easy navigation.

The first section highlights in a bigger card the Form Tutor, who plays a primary role in the student’s academic journey. This area shows the tutor’s photo, name, and  email, allowing parents to easily communicate when needed. 

Below, the Teachers section presents all the subject teachers in a card layout. Each card displays the teacher’s photo, name, and the subject they teach. This visual approach helps parents easily recognize and identify the educators involved in their child’s academic life.

A consistent footer, featuring the St. Paul’s School logo is placed at the bottom to reinforce the school’s identity.

<div align="center">
  <sub> Reports, Assignments and Timetable - User</sub><br>
  <img src="../assets/prototype4user.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=0-1&p=f&t=8VtQ5Ynbw4hI44c3-0">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 7 and 8 – Reports (User)
This screen allows parents to access and download their child’s academic reports. The familiar header remains at the top, ensuring consistent navigation.

The main content is organized in a simple table layout, where each row represents a term. A dropdown menu labeled "Select Year" allows parents to filter reports by year, enhancing usability. The use of icons such as a download button improves clarity and speeds up user interaction.

The school's primary colors are consistently applied, which helps reinforce the brand identity. The structured grid with enough padding between elements makes it easy to navigate without overwhelming the user.
  ## Screen 9 – Assignments (User)
This screen displays the student’s current and completed assignments. The layout is divided into two main sections: Assignments and Completed. Each assignment appears in a card format with the subject, task name and due date so that the parent can easily see the information. The consistent use of the school’s color palette helps maintain familiarity, making the platform intuitive. The layout also helps the parent to see first the assigned, since it is on top, and then the completed below, following an order of organization. 
  ## Screen 10 – Timetable (User)
The timetable screen presents the student’s weekly schedule. The layout is organized in a table format, divided into Week A and Week B, reflecting the school’s alternating weekly schedule.

Each square contains the subject name and the corresponding time slot, making it easy for parents to visualize their child's academic routine. The consistent use of the school’s color palette helps maintain familiarity, making the platform intuitive.

  ## Admins Desktop
<div align="center">
  <sub>Login and Home - Admin</sub><br>
  <img src="../assets/prototype1admin.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=1-1023&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 1 – Login (Admin)
The login screen is identical in style and structure to the desktop version used by parents. It maintains the school logo, a background image that reinforces identity, and input fields for email and password. 
  ## Screens 2, 3, 4 – Home (Admin)
The home page preserves the same visual hierarchy and structure as the desktop: the top navigation menu includes Change Student, Home, Academic Life (with fewer options, only Reports and Teachers since they are the only two pages they can edit in Academic Life), and Personal Information. The Events section shows upcoming events with scrollable cards. The Calendar download section is also present.

The key difference between the user desktop and the admin is that the admin has visual icons to add or edit any event or the calendar. If he presses any of those buttons a card will appear on the screen with text fields to add all the information necessary. 

<div align="center">
  <sub>Reports and Teachers - Admin</sub><br>
  <img src="../assets/prototype2admin.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=1-1023&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screens 5 and 6 – Reports (Admin)
The Reports screen mirrors the users purpose and content, allowing the admin to view and download academic reports. It includes: a dropdown menu to select the academic year and a table listing reports for each term.

The difference between the user desktop and the admin is that the admin has visual icons to add reports. Dropping a card that will allow him to add a file
  ## Screens 7, 8, 9 – Teachers (Admin)
The Teachers screen is consistent with the desktop layout but adapted structurally for responsive use: the Form Tutor section is highlighted with the teacher’s photo and contact information and the Subject Teachers grid shows all teachers linked to the student.

In the admins desktop he is able to visualise icons to add or edit any information about a student. If he presses any of those buttons a card will appear on the screen with text fields to add all the information necessary. 

  ## Users Mobile
<div align="center">
  <sub>Login and Student Selection - Mobile</sub><br>
  <img src="../assets/prototype1mobile.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=81-20&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 1 – Login (User Mobile)
The login screen mirrors the desktop version by maintaining a clean and focused design with the St. Paul’s logo, background image, and essential fields: email and password. While the desktop spreads the elements horizontally with more space, this version restructures them vertically to better fit different screen sizes, enhancing accessibility without losing brand identity.
  ## Screen 2 – Student Selection (User Mobile)
The student selection screen keeps the same visual language as the desktop, using cards with the student’s photo, name, year, and house. However, while the desktop displays the cards with more horizontal spacing, this version organizes them in a vertical stacked format to accommodate smaller screens. The notification icon remains consistent, reinforcing the visual connection between platforms.

<div align="center">
  <sub>Home - Mobile</sub><br>
  <img src="../assets/prototype2mobile.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=81-20&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screens 3, 4 and 5 – Home (User Mobile)
The home page retains the same structure as the desktop but the top menu changes to be more accessible to a smaller screen, that way the information such as Change Student, Home, Academic Life (with Reports, Assignments, Timetable and Teachers that appear when the user presses Academic Life), and Personal Information appear after the user presses the three lines, making the background (the home page) visible but darker to reduce attention and opening a right sidebar with the pages. The Events section with scrollable event cards. And the Calendar download section.

While the desktop uses a horizontal layout with more visible content at once, the mobile or responsive version reorganizes these sections vertically which means that Events, for example, feets less content.

<div align="center">
  <sub>Personal Information and Teachers - Mobile</sub><br>
  <img src="../assets/prototype3mobile.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=81-20&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 6 – Personal Information (User Mobile)
Just like on the desktop, this screen is divided into two key areas: Parent Personal Information (editable fields such as name, date of birth, address, phone, email, and profile photo) and Student Medical Information (with fields for medical notes like allergies).

The desktop version spreads these sections side by side, while this version stacks them vertically for better use of screen space. The presence of the red Save button and clear labeling maintains consistency between the two formats.
  ## Screen 7 – Teachers (User Mobile)
The Teachers screen reflects the same purpose and layout logic as the desktop. It features: a bigger Form Tutor card with a photo and contact email and a grid of Subject Teachers.

On the desktop, the teacher cards are arranged in rows with more horizontal space and more teachers in each row, whereas in this version, they are stacked in a vertical grid and only have space for two teachers per row.  

<div align="center">
  <sub>Reports - Mobile</sub><br>
  <img src="../assets/prototype4mobile.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=81-20&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screens 8 and 9 – Reports (User Mobile)
The reports section is highly consistent with the desktop version. Parents can select the academic year from a dropdown and download reports for each term. While the desktop uses a table with more columns visible at once, this layout prioritizes vertical stacking or pop-up lists.

<div align="center">
  <sub>Assignments and Timetable - Mobile</sub><br>
  <img src="../assets/prototype5mobile.png" width="85%"><br>
    <a href="https://www.figma.com/design/ALgXyHbLoRlmlAboRwmO3V/prototipo-alta-qualidade?node-id=81-20&p=f&t=QQ0HmJ3oLmxAeI3L-0" target="_blank">
    <sup>Link Figma</sup>
  </a><br>
</div>

  ## Screen 10 – Assignments (User Mobile)
This screen presents Current Assignments and Completed Assignments, similarly to the desktop. The primary difference is structural: while the desktop can show sections in a wider table, this version stacks items vertically making them smaller. Each assignment card shows the subject, task name, and due date.
  ## Screen 11 – Timetable (User Mobile)
The timetable preserves the same data structure as the desktop, including the Week A and Week B model. The desktop shows a full table view with all days visible horizontally, whereas the responsive version restructures it into a vertically scrollable format so that the parents can visualize it in a clear and intuitive way.

## 3.5. Modelagem do banco de dados (sprints 2 e 4)

### 3.5.1. Modelo relacional (sprints 2 e 4)
 

The relational model, or logical model, is the modeling that follows the conceptual model. It is created based on a type of database, such as Oracle, MySQL, SQL Server, among others. Its purpose is to outline the tables, the relationships between them, and define the data types that will be used in the columns.

<div align="center">
  <sub>Relational Model</sub><br>
  <img src="../assets/modeloRelacional.png" width="85%"><br>
</div>

With this, we are able to model the entire representation of how the database functions with the tables and the information that will be necessary, as will be shown below:



### **•Parents**

In the "parents" table, which is intended to store information about the student's parents, the following information will be required: first name, last name, phone number, email, address, postal code, photo, and password. This table also includes the person_ID, which, in simple terms, is the number that identifies each parent.

### **•Year**

In the "year" table, which is intended to store information about the year the student is studying at St. Paul's, the following information will be required: date of birth and the student's identifier (school_ID).

### **•Students**

In the "students" table, which is intended to store information about the students at St. Paul's, the following information will be required: first name, last name, photo, tutor, notifications, school house, class_id (which is the number that associates the student to the classroom), school_id (which is the number that identifies each student in the table), and person_id (which will be used to associate the parents with the students).

### **•Teachers**

In the "teachers" table, which is intended to store information about the teachers at St. Paul's School and the parents, the following information will be required: first name, last name, and email.

### **•Subjects**

In the "subjects" table, which is intended to store information about the subjects at St. Paul's School, the following information will be required: the subject name and its code. Each subject will be identified by a Subject_ID.

### **•Classes**

In the "classes" table, which is intended to organize the students into classes, the following information will be stored: the year code, the subject identifier, and the identifier of the responsible teacher. Each class will be identified by a class_ID.

###  **•Timetable**

In the "timetable" table, which is intended to record the class schedules at St. Paul's School, the following information will be stored: the day of the week, the start and end time of the class, the room where the class will take place, the schedule cycle, the class code (class_ID), and the student identifiers (School_ID). Each entry in the table will be identified by a Timetable_ID.

### **•Events**

In the "events" table, which is intended to store information about events held at St. Paul's School, the following information will be stored: the event title, description, event date and time, a photo, and the year identifier (year_ID) related to the event. Each event will be identified by an event_ID.

### **•Administrators**

In the "administrators" table, which is intended to store the data of the platform administrators at St. Paul's School, the following information will be required: first name, last name, email, and password of the administrator. Each administrator will be identified by an admin_ID.

### **•Medical Info**

In the "medical_info" table, which is intended to store medical information about the students at St. Paul's School, the following information will be stored: medical observations and medical contacts. The information is associated with the student through the School_ID.

### **•Reports**

In the "reports" table, which is intended to store the student's report cards at St. Paul's School, the following information will be stored: the term, academic year, the file path of the PDF, and the student identifier (School_ID). Each report will be identified by a report_ID.

### **•Assignments**

In the "assignments" table, which is intended to store the student's assignments or tasks at St. Paul's School, the following information will be stored: the task title, description, due date, a link to Teams, and the student identifier (School_ID). Each assignment will be identified by an assignment_ID.
### 3.5.2. Consultas SQL e lógica proposicional (sprint 2)

#1 | ---
--- | ---
**SQL expression** | SELECT * FROM students <br> WHERE (first_name LIKE 'D%' OR last_name LIKE 'P%') <br> AND year_code > '100'; 
**Logical propositions** | $A$: The first name starts with letter D <br> $B$: The Last name starts with letter P <br> $C$: The year code is bigger than 100
**Propositional logical expression** | $(A \lor B) \land C$
**Truth Table** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$(A \lor B)$</th> <th>$(A \lor B) \land C$</th> </tr> </thead> <tbody> <tr> <td>T</td> <td>T</td> <td>T</td> <td>T</td> <td>T</td> </tr> <tr> <td>T</td> <td>T</td> <td>F</td> <td>T</td> <td>F</td> </tr> <tr> <td>T</td> <td>F</td> <td>T</td> <td>T</td> <td>T</td> </tr> <tr> <td>T</td> <td>F</td> <td>F</td> <td>T</td> <td>F</td> </tr> <tr> <td>F</td> <td>T</td> <td>T</td> <td>T</td> <td>T</td> </tr> <tr> <td>F</td> <td>T</td> <td>F</td> <td>T</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>T</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> </tbody> </table>

#2 | ---
--- | ---
**SQL expression** | UPDATE medical_info <br> SET medical_notes = 'Nenhuma restrição médica'<br> WHERE class_ID = 5; <br> AND medical_notes IS NULL; <br> AND year_code = '2025'; 
**Logical propositions** | $A$: The class ID is 5<br> $B$: The medical notes is not null<br> $C$: The year code is 2025
**Propositional logical expression** | $A \land ( \lnot B ) \land C$ 
**Truth Table** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$\lnot B$</th><th>$(A \land \lnot B)$</th><th>$A \land \lnot B \land C$</th></tr></thead><tbody><tr><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>T</td><td>T</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>F</td><td>F</td><td>T</td><td>T</td><td>F</td></tr><tr><td>F</td><td>T</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>F</td><td>T</td><td>F</td><td>F</td></tr></tbody></table>

#3 | ---
--- | ---
**SQL expression** | DELETE FROM students <br> WHERE class_ID = 5 <br> AND (year_code = '2022' OR year_code = '2020') <br> AND class_ID IN (SELECT class_ID FROM classes WHERE teacher_ID = 1010);  
**Logical propositions** | $A$: The class ID is 5 <br> $B$: The year code is 2022 <br> $C$: The year code is 2020 <br> $D$: The teacher ID associated with the class is 1010
**Propositional logical expression** | $( A \land (B \lor C)) \land D$
**Truth Table** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$D$</th><th>$B \lor C$</th><th>$A \land (B \lor C)$</th><th>$(A \land (B \lor C)) \land D$</th></tr></thead><tbody><tr><td>T</td><td>T</td><td>T</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>T</td><td>T</td><td>F</td><td>T</td><td>T</td><td>F</td></tr><tr><td>T</td><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>T</td><td>F</td><td>F</td><td>T</td><td>T</td><td>F</td></tr><tr><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>F</td><td>T</td><td>F</td><td>T</td><td>T</td><td>F</td></tr><tr><td>T</td><td>F</td><td>F</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>T</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>T</td><td>F</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>F</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>F</td><td>F</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>T</td><td>F</td><td>T</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>F</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td></tr></tbody></table>

#4 | ---
--- | ---
**SQL expression** | SELECT * FROM suppliers <br> WHERE (state = 'California' AND supplier_id <> 900) <br> OR (supplier_id = 100); 
**Logical propositions** | $A$: O estado é 'California' (state = 'California') <br> $B$: O ID do fornecedor não é 900 (supplier_id ≠ 900) <br> $C$: O ID do fornecedor é 100 (supplier_id = 100)
**Propositional logical expression** | $(A \land B) \lor C$
**Truth Table** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$(A \land B)$</th> <th>$(A \land B) \lor C$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table>

#5 | ---
--- | ---
**SQL expression** | SELECT DISTINCT form_tutor <br> FROM students <br> WHERE School_ID = 123 <br> AND (first_name LIKE 'Jo%' OR year_code IN ('2023''2024') ) <br> AND form_tutor IS NOT NULL;
**Logical propositions** | $A$: The school ID is 123<br> $B$: The first name starts with “Jo” $C$: The year code is between 2023 and 2024 <br> $D$: The name of the form tutor is null
**Propositional logical expression** | $A \land$ $(B \lor C)$ $ \lnot D$ 
**Truth Table** | <table><thead><tr><th>$a$</th><th>$b$</th><th>$c$</th><th>$d$</th><th>$b \lor c$</th><th>$a \land (b \lor c)$</th><th>$\lnot d$</th><th>$a \land (b \lor c) \land \lnot d$</th></tr></thead><tbody><tr><td>T</td><td>T</td><td>T</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>T</td><td>T</td><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>T</td><td>T</td><td>F</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td></tr><tr><td>T</td><td>F</td><td>T</td><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td></tr><tr><td>T</td><td>F</td><td>F</td><td>T</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>T</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>T</td><td>F</td></tr><tr><td>F</td><td>T</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>T</td><td>F</td><td>T</td><td>F</td><td>T</td><td>F</td></tr><tr><td>F</td><td>T</td><td>F</td><td>T</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>T</td><td>F</td><td>F</td><td>T</td><td>F</td><td>T</td><td>F</td></tr><tr><td>F</td><td>F</td><td>T</td><td>T</td><td>T</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>T</td><td>F</td><td>T</td><td>F</td><td>T</td><td>F</td></tr><tr><td>F</td><td>F</td><td>F</td><td>T</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>T</td><td>F</td></tr></tbody></table>

## 3.6. WebAPI e endpoints (sprints 3 e 4)

### Login Page

#### get/login

- Method: GET  
- Description: Loads the login interface where the user can enter their credentials.  
- Headers: None required  
- Body: Not applicable  
- Authentication: Not required  
- Response:  
  - Status 200: Renders the login.ejs page with input fields for email and password.  
  - Status 500: In case of internal server error, renders the error page with a general message.

#### post/login

- Method: POST  
- Description: Receives login form data and verifies user credentials. On success, starts a session and redirects to the homepage. On failure, returns to the login page with an error message.  
- Headers: Content-Type: application/x-www-form-urlencoded  
- Body Parameters:  
  - email (string) – User's email (required)  
  - password (string) – User's password (required)  
- Authentication: Not required  
- Response:  
  - Status 302 (Redirect): Redirects to /home after successful authentication.  
  - Status 401: Reloads the login page with the message "Invalid email or password."  
  - Status 500: On internal server error, renders a generic error page.

#### User Story Connection

The /login endpoint enables the user authentication functionality required by US01. It validates the parent’s credentials (email and password) and, if correct, initiates a session that allows access to the Parent Portal. This functionality meets the acceptance criteria CR1, which states that a parent with a valid registration should be able to log in to the Parent Portal.

---

### Select Student Page

#### get/select-student

- Method: GET  
- Description: Renders the student selection screen for parents with more than one child registered in the system. It allows the parent to choose which child's academic data they wish to view in the portal.  
- Headers:  
  - Cookie: sessionId=<session_id> – Used to maintain user session and identify the logged-in parent.  
- Request Body:  
  - Not applicable (GET request).  
- Response:  
  - Status 200: Returns an HTML page listing all the children associated with the parent.  
  - Status 401 or Redirect: If the user is not authenticated, they are redirected to the login page.

#### User Story Connection

The /select-student endpoint fulfills US04 by allowing parents with multiple children to view a selection interface, enabling them to choose whose academic data they want to access. It satisfies the acceptance criterion CR1, which requires that parents with multiple linked children can select between them.

---

### Home Page

#### get/home

- Method: GET  
- Description: Renders the home page for authenticated users (both parents and administrators). For parents, it displays upcoming events relevant to their child’s school year. For admins, it shows all upcoming school events and provides access to event management.  
- Headers:  
  - Cookie: sessionId=<session_id> – Maintains the user’s session and identifies the logged-in user.  
- Request Body:  
  - Not applicable (GET request).  
- Response:  
  - Status 200: Returns an HTML page showing:  
    - Welcome message with the user’s name.  
    - Academic calendar download link.  
    - List of upcoming events relevant to the user.  
    - Admins also see an "Add New Event" button.  
  - Status 401 or Redirect: Redirects to the login page if the user is not authenticated.

#### User Story Connection

The /home endpoint is the central landing page after login, displaying contextual academic data (events) specific to the selected child. It helps parents stay updated and informed, fulfilling CR1 of US02.

For admin users, the home page provides access to event creation (/events/add), enabling them to manage and communicate school events. This fulfills CR1 of US12.

---

### Reports Page

#### get/reports

- Method: GET  
- Description: Renders the report cards page where parents can view their child's academic performance reports. The page also provides the functionality to download the reports as PDF files.  
- Headers:  
  - Cookie: sessionId=<session_id> – Maintains the user’s session and identifies the logged-in parent.  
- Request Body:  
  - Not applicable (GET request).  
- Response:  
  - Status 200: Returns an HTML page displaying:  
    - A list of report cards linked to the selected student.  
    - Buttons or links for each report to view details or download the report in PDF format.  
  - Status 401 or Redirect: Redirects to the login page if the user is not authenticated.  
  - Status 404: If no reports are found for the student, a friendly message is displayed.

#### User Story Connection

The /reports GET endpoint supports the ability for parents to access detailed academic reports of their children and provides an option to download those reports as PDFs, directly addressing CR1 of US07.

---

### Teachers Page

#### get/teachers

- Method: GET  
- Description: Renders the page displaying all teachers responsible for the student’s classes. Parents can view the contact information and relevant details of each teacher to facilitate communication.  
- Headers:  
  - Cookie: sessionId=<session_id> – Maintains the user’s session and identifies the logged-in parent.  
- Request Body:  
  - Not applicable (GET request).  
- Response:  
  - Status 200: Returns an HTML page listing all teachers linked to the student, showing their contact information and other relevant data.  
  - Status 401 or Redirect: Redirects to the login page if the user is not authenticated.  
  - Status 404: Displays a message if no teacher information is found.

#### User Story Connection

The /teachers GET endpoint provides parents with access to their child's teachers’ contact information, fulfilling the acceptance criteria CR1 of US03.

---

#### post/teachers/save

- Method: POST  
- Description: Handles the creation of a new teacher or the update of an existing teacher's information. Administrators use this endpoint to add new teachers to the system or to edit details such as name, email, and photo URL.  
- Headers:  
  - Cookie: sessionId=<session_id> – Maintains the user’s session and identifies the logged-in administrator.  
- Request Body:  
  - Form data (application/x-www-form-urlencoded or multipart/form-data) containing the following fields:  
    - first_name (string, required): Teacher’s first name.  
    - last_name (string, required): Teacher’s last name.  
    - email (string, required): Teacher’s email address.  
    - photo (string, optional): URL to the teacher’s photo. Can be empty.  
- Response:  
  - Status 302 (Redirect): Redirects to the /teachers page after successful creation or update.  
  - Status 400: Returns an error if required fields are missing or invalid.  
  - Status 401 or Redirect: Redirects to login page if the user is not authenticated or authorized.  
  - Status 500: Returns an error page if there is a server error during the save process.

#### User Story Connection

The /teachers/save POST endpoint allows administrators to add new teachers or update existing teacher information, fulfilling the acceptance criteria CR1 of US14 by ensuring data is current and accurate in the system.

---

#### post/teachers/:id/delete

- Method: POST  
- Description: Deletes a specific teacher from the system based on the provided ID. This function is only accessible by administrators.  
- Headers:  
  - Cookie: sessionId=<session_id> – Maintains the admin user’s session.  
- Body Parameters:  
  - Not applicable (teacher ID is in the URL).  
- Authentication: Required (Administrator)  
- Response:  
  - Status 302 (Redirect): Redirects back to /teachers after successful deletion.  
  - Status 401 or Redirect: Redirects to login if the user is not authenticated.  
  - Status 500: On internal error, renders an error page.

#### User Story Connection

The /teachers/:id/delete endpoint supports US14 by allowing administrators to remove teacher records when necessary, fulfilling CR2 which requires that outdated or incorrect teacher data can be deleted from the system.

---

### Timetable Page

#### get/timetable

- Method: GET  
- Description: Displays the timetable (weekly class schedule) for the selected student. Shows subjects, times, and assigned teachers per weekday.  
- Headers:  
  - Cookie: sessionId=<session_id> – Identifies the logged-in parent.  
- Body Parameters:  
  - Not applicable (GET request).  
- Authentication: Required (Parent)  
- Response:  
  - Status 200: Renders the timetable.ejs page with the schedule.  
  - Status 401 or Redirect: Redirects to login if not authenticated.  
  - Status 404: If no schedule is found, displays a friendly message.

#### User Story Connection

This endpoint fulfills US05 by allowing parents to view their child's weekly academic schedule. It supports CR1, which requires that parents can see the timetable associated with their selected student.

---

#### post/timetable/save

- Method: POST  
- Description: Allows administrators to create or edit a timetable entry, defining the subject, weekday, start time, end time, and assigned teacher for a specific class.  
- Headers:  
  - Cookie: sessionId=<session_id> – Identifies the logged-in administrator.  
- Body Parameters:  
  - class_id (integer, required) – ID of the class.  
  - subject_id (integer, required) – ID of the subject.  
  - teacher_id (integer, required) – ID of the assigned teacher.  
  - weekday (string, required) – Weekday name (e.g., Monday).  
  - start_time (string, required) – Start time of the lesson.  
  - end_time (string, required) – End time of the lesson.  
- Authentication: Required (Administrator)  
- Response:  
  - Status 302 (Redirect): Redirects back to /timetable after successful creation or update.  
  - Status 400: Returns an error if required fields are missing or invalid.  
  - Status 401 or Redirect: Redirects to login if not authenticated.  
  - Status 500: On internal error, shows a generic error page.

#### User Story Connection

This endpoint supports US13 by giving admins the ability to manage academic schedules. It satisfies CR1 and CR2, ensuring timetable data can be both created and edited dynamically.

---

#### post/timetable/:id/delete

- Method: POST  
- Description: Deletes a specific timetable entry from the database using its ID. Only administrators have access to this operation.  
- Headers:  
  - Cookie: sessionId=<session_id> – Identifies the logged-in administrator.  
- Body Parameters:  
  - Not applicable (ID in URL).  
- Authentication: Required (Administrator)  
- Response:  
  - Status 302 (Redirect): Redirects to /timetable after successful deletion.  
  - Status 401 or Redirect: Redirects to login if the user is not authenticated.  
  - Status 500: On error, returns a general error page.

#### User Story Connection

This endpoint also supports US13 by enabling administrators to remove outdated or incorrect entries in the school timetable, fulfilling CR3 (removal of timetable entries).

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

## 4.1. First Version of the Web Application

During Sprint 3, we completed the first functional implementation of the web system, structuring the entire application based on the **MVC (Model-View-Controller)** architectural pattern. We developed and integrated the **model**, **controller**, and **view** files according to the previously defined architecture diagram, connecting them directly to the database to ensure data persistence and efficient retrieval.

In addition, we implemented the main **routes** of the application, defining the navigation flow between pages such as login, about, home, personal information, reports, assignments, teachers, and timetable. The behavior and navigation of the system were developed in accordance with the **high-fidelity prototype**, respecting the expected user flow and interactions.

During the process, we encountered important technical challenges, such as:
- Establishing proper communication between controllers, models, and views.
- Correctly routing requests to the corresponding views.
- Creating endpoints for each page’s specific functionality.
- Persisting user login and maintaining an active session.

After solving these challenges, we finalized an initial, unstyled version of the application, ensuring that each page has its basic functional structure implemented and integrated with the database.

### Application Examples:

Here, we present a practical example of how we applied the **MVC (Model-View-Controller)** architectural pattern in the login functionality of our application. The goal is to illustrate how the system's three main layers interact with each other to perform the full user authentication flow.

![image](https://github.com/user-attachments/assets/33fe930a-7637-4bf5-9b50-ff8ad0bac535)

This snippet shows the `findAnyUserByEmail` function, which searches the database to check whether the provided email belongs to a parent or an administrator. The method returns the user data along with their role, which is essential for managing system permissions.

![image](https://github.com/user-attachments/assets/f84cabac-fe9d-47de-b0ca-c6699571dffd)

Here we have the login controller, which handles the authentication logic. It checks if the email is registered, compares the entered password with the stored one using `bcrypt`, and if successful, starts the user session. Otherwise, it returns appropriate error messages.

![image](https://github.com/user-attachments/assets/a2038811-0264-4b0a-94ab-c5affc216062)

These routes define the application's paths. The `GET /` route renders the login page, while the `POST /` route handles the form submission and triggers the controller responsible for user validation. This separation ensures structured and secure navigation.

![image](https://github.com/user-attachments/assets/444905dc-8da2-49ab-9326-12c2971acd91)

The view displays the login form rendered to the user. With email and password fields, it uses the `<form>` tag to submit data via the `POST` method, triggering the logic defined in the controller. This layer is responsible for direct user interaction.

These examples demonstrate how we applied the MVC pattern to ensure code organization, separation of concerns, and easier maintainability of the project.

### Next Steps:
- Apply **CSS styling** for each page, following the high-fidelity prototype and the style guide defined in this sprint.
- Ensure **responsiveness** for smooth usage on both mobile and desktop devices.
- Conduct usability tests and begin refining the visual and functional aspects.
- Organize and standardize the codebase, following best programming practices.

## 4.2. Segunda versão da aplicação web (sprint 4)

*Descreva e ilustre aqui o desenvolvimento da sua segunda versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

## 4.3. Versão final da aplicação web (sprint 5)

*Descreva e ilustre aqui o desenvolvimento da última versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados (sprint 4)

*Liste e descreva os testes unitários dos endpoints criados, automatizados e planejados para sua solução. Posicione aqui também o relatório de cobertura de testes Jest se houver (através de link ou transcrito para estrutura markdown)*

## 5.2. Testes de usabilidade (sprint 5)

*Posicione aqui as tabelas com enunciados de tarefas, etapas e resultados de testes de usabilidade. Ou utilize um link para seu relatório de testes (mantenha o link sempre público para visualização)*

# <a name="c6"></a>6. Market Research and Marketing Plan

## 6.1 Executive Summary

The project involves the development of an institutional web application aimed at parents and guardians of students at St. Paul’s School, with the goal of centralizing and facilitating access to academic, administrative, and school communication information. The solution responds to a clear market opportunity: the growing need for digital tools that promote transparency, family engagement, and efficient integration between the school and its community.

Currently, many educational systems operate in a fragmented way, using different channels to communicate essential information such as grades, timetables, events, and assignments. This fragmentation can lead to communication gaps and low parental involvement in student's daily school life. The developed application stands out by offering a centralized experience with intuitive navigation and personalized content according to the user's profile (guardian or administrator).

Key competitive advantages of the solution include an interface tailored to the institutional identity of St. Paul’s School, user-friendly access across multiple devices, and a modular structure based on MVC architecture, which facilitates continuous maintenance and evolution of the platform. In addition, the application incorporates institutional visual elements that reinforce the school’s identity and create a personalized and easy-to-use digital environment.

From a strategic perspective, the system aims to strengthen the relationship between the school and families, increase communication efficiency, reduce administrative rework, and consolidate the institution’s image as an educational innovator. The solution aligns with the school’s goals of promoting transparent, connected, and student-centered education, expanding the impact of the educational experience beyond the classroom.

## 6.2 Análise de Mercado

a) Industry Overview

The EdTech sector in Brazil is constantly expanding, driven by the growing demand for innovative solutions in schools and by the increased investment in digital infrastructure. Brazil is currently the leader in the Latin American EdTech market, hosting approximately 70% of the region's startups and attracting around USD 475 million in investments between 2015 and 2024. <a href="https://startups.com.br/pesquisas/brasil-lidera-mercado-latam-de-edtechs-e-movimenta-us-475m-em-10-anos" target="_blank">Link site Startups</a>
A growing subsegment within EdTech is the development of digital communication platforms that connect schools and families. These tools play a vital role in modern education by improving the efficiency, speed, and clarity of interactions between educators and parents. According to Sponte (2023), apps and digital agendas simplify the exchange of academic, behavioral, and administrative information, fostering transparency and greater parental engagement. <a href="https://www.sponte.com.br/blog/a-comunicacao-entre-pais-e-escola-o-que-a-tecnologia-tem-a-nos-ensinar?utm_source=chatgpt.com" target="_blank">Link site Sponte</a>
From a regulatory perspective, Brazil’s Law No. 15.100/2025 promotes the responsible use of digital technologies in education, encouraging schools to adopt structured and ethical digital communication practices. <a href="https://www.gov.br/mec/pt-br/assuntos/noticias/2025/janeiro/sancionada-lei-que-restringe-uso-de-celulares-nas-escolas" target="_blank">Link site Gov</a>
Within this context, platforms like the Parent Portal being developed by us for St. Paul’s School is well positioned to meet both market demand and regulatory expectations. By enabling secure, real-time communication between families and the school, the platform enhances transparency, supports educational success, and reflects broader trends in digital transformation within private education.


b) Market Size and Growth
The global educational apps market was valued at approximately USD 60 billion in 2024 and is projected to reach USD 150 billion by 2033, with a compound annual growth rate (CAGR) of 10.5% from 2026 to 2033. <a href="https://www.verifiedmarketreports.com/pt/product/education-apps-market/" target="_blank">Link site Verified Market Reports</a>
In Brazil, the EdTech segment is gaining traction, accounting for 70% of Latin America's EdTech startups and moving over USD 475 million in investments over the past decade. This momentum is a response to schools’ increasing need to modernize teaching and streamline communication and administrative processes.<a href="https://startups.com.br/pesquisas/brasil-lidera-mercado-latam-de-edtechs-e-movimenta-us-475m-em-10-anos" target="_blank">Link site Startups</a>
Specifically, the niche of school-family communication platforms has seen a sharp rise in adoption, especially among private institutions seeking to engage parents more actively in the educational process. While there is limited quantitative data specific to this niche, the broader trend of school digitalization indicates a promising and expanding market for solutions like the Parent Portal.

c)Market Trends
The EdTech sector continues to evolve rapidly, shaped by technological innovation, changing family expectations, and shifts in how schools approach communication and management. Within this landscape, several key trends are driving the development and adoption of platforms like the Parent Portal: Hybrid Learning, Game-Based Learning, Artificial Intelligence (AI), Mobile Learning and much more.
These trends highlight the sector's evolution toward greater technological integration, aiming to enhance the quality of education and foster stronger school-family partnerships. The St. Paul’s Parent Portal is well-positioned within this landscape, offering a modern platform that supports communication, transparency, and efficiency in educational management.

## 6.3 Competitor Analysis
### a) Competitive Landscape
The market for educational solutions focused on communication between schools and guardians includes both direct and indirect competitors. Direct competitors include platforms such as Sponte, Agenda Edu, and ClassApp, which offer broad school-focused solutions with features like messaging, notifications, and activity tracking. These tools are well-established in the market and cater to a wide range of institutions, but they often lack deep customization or seamless integration with each school's specific context.

Among the indirect competitors are tools such as Google Classroom and WhatsApp groups, which, although not specifically designed for school-family communication, are frequently used as informal substitutes for this purpose.

These competitors generally position themselves as generic solutions designed for schools of various sizes, where scalability and broad feature sets are prioritized over a tailored user experience. This approach may lead to platforms that fail to reflect the unique identity and operational culture of institutions like St. Paul’s School, which has over 100 years of tradition and requires a system that addresses its personalized needs.

### b) Our Strategic Differentiation
Our solution stands out by being the result of in-depth research on St. Paul’s School, taking into account both its identity as an educational institution and its operations as an organization—based on thorough analyses and tailored presentations. Rather than imposing a generic tool, we committed to understanding the school's specific challenges and transforming its pain points into strengths through technology.

While traditional platforms offer broad but rigid toolsets, our application was built as a custom-fit response, based on real diagnostics. The result is a portal that integrates grades, attendance, event calendars, announcements, feedback, teacher profiles, and more—within a secure, intuitive environment.

Another key differentiator is our ability to continuously adapt. We work side by side with the school, enabling ongoing adjustments and feature additions whenever necessary. This flexibility strengthens the relationship between the school and its families.

Choosing our solution means choosing a tool that truly understands the institution and its unique needs—offering efficiency, personalization, and evolution. These are the qualities that position us ahead of the competition.

## 6.4 Target Audience

### a) Market Segmentation
The application is aimed at the high-income private basic education segment, specifically international and bilingual schools located in major urban centers. According to data from the 2023 School Census (INEP), Brazil has over 18,000 private schools, with the Southeast region accounting for approximately 40% of them. Among these, there is a growing niche of international schools serving families with high purchasing power and multicultural profiles.

Furthermore, a report from ABRAFI (Brazilian Association of International Schools) highlights an increasing demand for digital solutions that strengthen the relationship between school and family—especially after the pandemic, which accelerated the sector's digital transformation. Schools that offer personalized experiences for parents have stood out in terms of student retention and loyalty.

This application, therefore, targets high-end schools seeking technological differentiation and valuing structured and centralized institutional communication. The target market also includes pedagogical and administrative teams that need centralized tools to reduce redundant work and improve communication with families.

### b) Target Audience Profile
The application’s target audience is composed of parents and guardians of students enrolled at St. Paul’s School, an international institution located in São Paulo with over 100 years of tradition. This group has a high level of education, elevated income, a multicultural profile, and places great value on academic excellence.

Demographically, they are adults aged 30 to 55, primarily residing in the São Paulo metropolitan area. Many are professionals, executives, or expatriates, fluent in English and with high expectations regarding the school’s quality of service.

Psychographically, they value transparency in school relationships, student autonomy, and the use of technology that optimizes time and enhances the educational experience. They are sensitive to factors such as safety, innovation, pedagogical excellence, and institutional reputation.

Behaviorally, they are digitally literate users who expect simplicity in navigation, centralized information, and intuitive design. Their main needs include:

- Monitoring their children's academic performance;

- Receiving updates on school events and assignments;

- Quickly accessing documents, timetables, and announcements.

All of this should be provided in a centralized and easily accessible manner, accommodating the demanding daily routines of parents and guardians.

*Sources: INEP (2023 School Census), ABRAFI (2023), IBGE (PNAD 2022), Internal persona analysis—St. Paul’s School.*

## 6.5 Posicionamento

a) Unique Value Proposition 
The St. Paul’s Parent Portal was designed to offer parents a seamless, intuitive, and comprehensive digital experience, strengthening the connection between school and families. Unlike generic platforms, our portal is fully tailored to the culture, values, and processes of St. Paul’s, providing quick and secure access to academic information, student performance, schedules, assignments, personal details, and progress reports.
Our key value lies in centralizing all essential information in one place, allowing parents to monitor their children’s educational journey easily and in real time. The portal empowers families by fostering greater involvement and engagement in students' academic development. More than just an information tool, the Parent Portal serves as an active bridge between the school and families, supporting the pursuit of educational excellence that defines St. Paul’s.

b) Differentiation Strategy 
The St. Paul’s Parent Portal stands out for its complete customization, designed specifically to meet the unique needs of the St. Paul’s school. While other platforms offer generic solutions, our portal is fully aligned with the school’s internal processes, bilingual approach, and academic standards.
A key differentiator is the focus on user experience, offering a clean, intuitive interface accessible on both desktop and mobile devices. The portal features clear navigation, parent-friendly language, and tools designed for speed and convenience, such as smart notifications and quick access to essential data.
Additionally, the seamless integration with the school’s internal academic systems ensures that parents always receive accurate, real-time, and secure information. The St. Paul’s Parent Portal is positioned not only as a digital platform but also as a strategic communication and engagement channel, fully aligned with the school’s commitment to excellence.

## 6.6 Estratégia de Marketing 

**Produc/Service:** Our web application is a platform designed to centralize key academic and administrative information for families at Saint Paul’s School, facilitating the communication between these two. Its main functionalities include access to report cards, attendance records, event calendars, announcements, and real-time feedback, all in one secure and intuitive environment. The platform also features a dedicated teacher dashboard, aiming to improve both internal communication and the parent-school relationship.
Unlike generic educational software, our application was built based on a deep understanding of the school’s identity, operational needs, and communication gaps. This allows us to turn current weaknesses like fragmented information and scattered communication, into strengths.

**Price:** The pricing model is based on a one-time software purchase. Once acquired, the school owns a perpetual license to use the platform without recurring subscription fees. This approach eliminates ongoing costs and provides long-term value, especially for institutions looking to invest in a reliable and sustainable digital solution.
The price is scaled according to the necessities and size of the school, allowing for tailored packages that match each school’s specific needs. Compared to subscription-based competitors like ClassApp or Agenda Edu, our model offers full ownership, higher customization, and a better return on investment over time.

**Place:** The platform will be distributed entirely through digital channels. Access is web-based and hosted on a server, ensuring availability from any device with no need for local installation. For Saint Paul’s, the application will be paired to match with the school’s existing digital ecosystem, including the official website and communication systems.

**Promotion:** Our promotional strategy focuses on reaching schools representatives and administrators through educational conventions and industry. These conventions offer an opportunity to showcase our platform in person, demonstrating its benefits through live demos and personalized consultations. By attending these targeted events, we aim to build trust, answer their questions, and create relationships with key people .
In parallel, we will execute a digital marketing campaign through social media platforms such as LinkedIn and Instagram, where educational professionals and institutions actively engage. Paid ads will be used to boost visibility, targeting school administrators based on their roles and institutional profiles. This approach will help us generate awareness and drive traffic to our landing pages, where interested schools can schedule demos or request more information.


# <a name="c7"></a>7. Conclusões e trabalhos futuros (sprint 5)

*Escreva de que formas a solução da aplicação web atingiu os objetivos descritos na seção 2 deste documento. Indique pontos fortes e pontos a melhorar de maneira geral.*

*Relacione os pontos de melhorias evidenciados nos testes com planos de ações para serem implementadas. O grupo não precisa implementá-las, pode deixar registrado aqui o plano para ações futuras*

*Relacione também quaisquer outras ideias que o grupo tenha para melhorias futuras*

# <a name="c8"></a>8. Referências (sprints 1 a 5)

_Incluir as principais referências de seu projeto, para que seu parceiro possa consultar caso ele se interessar em aprofundar. Um exemplo de referência de livro e de site:_<br>

LUCK, Heloisa. Liderança em gestão escolar. 4. ed. Petrópolis: Vozes, 2010. <br>
SOBRENOME, Nome. Título do livro: subtítulo do livro. Edição. Cidade de publicação: Nome da editora, Ano de publicação. <br>

INTELI. Adalove. Disponível em: https://adalove.inteli.edu.br/feed. Acesso em: 1 out. 2023 <br>
SOBRENOME, Nome. Título do site. Disponível em: link do site. Acesso em: Dia Mês Ano

# <a name="c9"></a>Anexos

*Inclua aqui quaisquer complementos para seu projeto, como diagramas, imagens, tabelas etc. Organize em sub-tópicos utilizando headings menores (use ## ou ### para isso)*
