<h2>Bem-vindo(a), <%= user %>!</h2>
<h3>Selecione um aluno</h3>

<% if (students.length === 0) { %>
  <p>Nenhum aluno vinculado a este responsável.</p>
<% } else { %>
  <form action="/dashboard" method="POST">
    <label for="student">Aluno:</label>
    <select name="studentId" id="student" required>
      <% students.forEach(student => { %>
        <option value="<%= student.school_id %>">
          <%= student.first_name %> <%= student.last_name %>
        </option>
      <% }) %>
    </select>
    <br><br>
    <button type="submit">Confirmar</button>
  </form>
<% } %>
