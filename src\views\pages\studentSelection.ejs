<h2>Be<PERSON>-vindo(a), <%= user %>!</h2>
<h3>Selecione um aluno</h3>

<% if (students.length === 0) { %>
  <p>Nenhum aluno vinculado a este responsável.</p>
<% } else { %>
  <div style="display: flex; flex-wrap: wrap; gap: 20px;">
    <% students.forEach(student => { %>
      <form action="/dashboard" method="POST" style="border: 1px solid #ccc; border-radius: 10px; padding: 20px; width: 250px; text-align: center;">
        <input type="hidden" name="studentId" value="<%= student.school_id %>">
        
        <% if (student.photo) { %>
          <img src="<%= student.photo %>" alt="Foto de <%= student.first_name %>" style="width: 100px; height: 100px; border-radius: 50%; object-fit: cover; margin-bottom: 10px;">
        <% } else { %>
          <div style="width: 100px; height: 100px; border-radius: 50%; background-color: #eee; line-height: 100px; margin: 0 auto 10px;">Sem foto</div>
        <% } %>

        <h4><%= student.first_name %> <%= student.last_name %></h4>
        <p><strong>Ano:</strong> <%= student.year_code || 'N/A' %></p>
        <p><strong>Casa:</strong> <%= student.house || 'N/A' %></p>
        <p><strong>Notificações:</strong> <%= student.notifications || 'Nenhuma' %></p>

        <button type="submit" style="margin-top: 10px; padding: 8px 16px;">Selecionar</button>
      </form>
    <% }) %>
  </div>
<% } %>
