/* Reset básico */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  body {
    font-family: 'Segoe UI', 'Roboto', sans-serif;
    background-color: white;
    color: #333;
  }
  
  /* Container principal */
  .container {
    max-width: 1000px;
    margin: 40px auto;
    padding: 20px;
  }
  
  /* <PERSON><PERSON><PERSON><PERSON> */
  h2 {
    font-size: 2rem;
    margin-bottom: 24px;
    color: #111;
  }
  
  /* <PERSON><PERSON> de título (Assigned e Completed) */
  .section-title {
    background-color: #8b0015;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    margin-bottom: 10px;
    margin-top: 24px;
    font-weight: 600;
    width: 100%;
    display: inline-block;
  }
  
  /* Lista de tarefas */
  .assignments-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  /* Card de tarefa */
  .assignment-card {
    background-color: #001e2d;
    color: white;
    border-radius: 4px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .assignment-card:hover {
    background-color: #022c3e;
  }
  
  /* <PERSON><PERSON>es */
  .assignment-icon.assigned {
    font-size: 1.5rem;
  }
  
  /* Conteúdo textual da tarefa */
  .assignment-content {
    display: flex;
    flex-direction: column;
  }
  
  .assignment-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 1rem;
  }
  
  .assignment-subject {
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #ccc;
  }
  
  .assignment-date {
    font-size: 0.8rem;
    color: #bbb;
  }
  
  /* Card de tarefa concluída */

  .assignment-card.completed {
    background-color: #001e2d;
    color: white;
    border-radius: 4px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .assignment-card.completed:hover {
    background-color: #022c3e;
  }
  
  /* Ícones */
  .assignment-icon.completed {
    font-size: 1.5rem;
  }
  
  /* Conteúdo textual da tarefa */
  .assignment-content.completed {
    display: flex;
    flex-direction: column;
  }
  
  .assignment-title.completed {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 1rem;
  }
  
  .assignment-subject.completed {
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #ccc;
  }
  
  .assignment-date.completed {
    font-size: 0.8rem;
    color: #bbb;
  }
  
  /* Responsividade */
  @media (max-width: 768px) {
    .assignment-card {
      flex-direction: column;
      align-items: flex-start;
    }
  }
  