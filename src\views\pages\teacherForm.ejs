<div class="container">
  <h2><%= isEdit ? '<PERSON>ar Professor' : '<PERSON><PERSON><PERSON>r Professor' %></h2>
  
  <div class="form-container">
    <form action="/teachers/save<%= isEdit ? '/' + teacher.teacher_id : '' %>" method="POST">
      <div class="form-group">
        <label for="first_name">Nome:</label>
        <input type="text" id="first_name" name="first_name" value="<%= teacher.first_name %>" required>
      </div>
      
      <div class="form-group">
        <label for="last_name">Sobrenome:</label>
        <input type="text" id="last_name" name="last_name" value="<%= teacher.last_name %>" required>
      </div>
      
      <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" value="<%= teacher.email %>" required>
      </div>
      
      <div class="form-group">
        <label for="photo">URL da Foto:</label>
        <input type="text" id="photo" name="photo" value="<%= teacher.photo %>">
        <small>Deixe em branco para não usar foto</small>
      </div>
      
      <div class="form-actions">
        <button type="submit" class="btn btn-primary">Salvar</button>
        <a href="/teachers" class="btn btn-secondary">Cancelar</a>
      </div>
    </form>
  </div>
</div>