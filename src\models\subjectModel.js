const db = require('../config/db');

const findAllSubjects = async () => {
  const query = 'SELECT Subject_ID as subject_id, Name as name, Code as code FROM subjects ORDER BY Name';
  const { rows } = await db.query(query);
  return rows;
};

const findSubjectById = async (subjectId) => {
  const query = 'SELECT Subject_ID as subject_id, Name as name, Code as code FROM subjects WHERE Subject_ID = $1';
  const { rows } = await db.query(query, [subjectId]);
  return rows.length > 0 ? rows[0] : null;
};

module.exports = {
  findAllSubjects,
  findSubjectById
};