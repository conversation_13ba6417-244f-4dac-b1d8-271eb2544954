const db = require('../config/db');

const findAllTeachers = async () => {
  const query = 'SELECT t.teacher_id, t.first_name, t.last_name, t.email, t.photo FROM teacher t';
  const { rows } = await db.query(query);
  return rows;
};

const findFormTutorByStudentId = async (studentId) => {
  const query = 'SELECT form_tutor FROM students WHERE school_id = $1';
  const { rows } = await db.query(query, [studentId]);
  return rows.length > 0 ? rows[0].form_tutor : null;
};

const findTeacherById = async (teacherId) => {
  const query = 'SELECT * FROM teacher WHERE teacher_id = $1';
  const { rows } = await db.query(query, [teacherId]);
  return rows.length > 0 ? rows[0] : null;
};

const createTeacher = async (teacherData) => {
  const { first_name, last_name, email, photo } = teacherData;
  const query = `
    INSERT INTO teacher (first_name, last_name, email, photo)
    VALUES ($1, $2, $3, $4)
    RETURNING *
  `;
  const values = [first_name, last_name, email, photo];
  const { rows } = await db.query(query, values);
  return rows[0];
};

const updateTeacher = async (teacherId, teacherData) => {
  const { first_name, last_name, email, photo } = teacherData;
  const query = `
    UPDATE teacher
    SET first_name = $1, last_name = $2, email = $3, photo = $4
    WHERE teacher_id = $5
    RETURNING *
  `;
  const values = [first_name, last_name, email, photo, teacherId];
  const { rows } = await db.query(query, values);
  return rows[0];
};

const updateStudentFormTutor = async (studentId, formTutor) => {
  const query = `
    UPDATE students
    SET form_tutor = $1
    WHERE school_id = $2
    RETURNING *
  `;
  const { rows } = await db.query(query, [formTutor, studentId]);
  return rows[0];
};

module.exports = { 
  findAllTeachers, 
  findFormTutorByStudentId, 
  findTeacherById,
  createTeacher,
  updateTeacher,
  updateStudentFormTutor
};

