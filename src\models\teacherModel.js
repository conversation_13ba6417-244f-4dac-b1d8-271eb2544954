const db = require('../config/db');

const findAllTeachers = async () => {
  try {
    // Primeiro, vamos verificar a estrutura da tabela teacher
    const tableStructure = await db.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'teacher'
    `);
    console.log('Teacher table structure:', tableStructure.rows);
    
    // Vamos simplificar a consulta para evitar o erro
    const query = `
      SELECT t.teacher_id, t.first_name, t.last_name, t.email, t.photo
      FROM teacher t
    `;
    const { rows } = await db.query(query);
    
    // Depois de obter os professores, vamos buscar as classes para cada um
    for (const teacher of rows) {
      // Buscar classes
      const classesQuery = `
        SELECT c."class-code" 
        FROM classes c 
        WHERE c.teacher_ID = $1
      `;
      const classesResult = await db.query(classesQuery, [teacher.teacher_id]);
      teacher.classes = classesResult.rows.map(row => row["class-code"]).filter(Boolean);
    }
    
    return rows;
  } catch (error) {
    console.error('Error in findAllTeachers:', error);
    // Retornar um array vazio em caso de erro para evitar quebrar a aplicação
    return [];
  }
};

const findFormTutorByStudentId = async (studentId) => {
  const query = 'SELECT form_tutor FROM students WHERE school_id = $1';
  const { rows } = await db.query(query, [studentId]);
  console.log('Form tutor query result:', rows); // Log para debug
  return rows.length > 0 ? rows[0].form_tutor : null;
};

const findTeacherById = async (teacherId) => {
  const query = 'SELECT * FROM teacher WHERE teacher_id = $1';
  const { rows } = await db.query(query, [teacherId]);
  return rows.length > 0 ? rows[0] : null;
};

const createTeacher = async (teacherData) => {
  const { first_name, last_name, email, photo } = teacherData;
  const query = `
    INSERT INTO teacher (first_name, last_name, email, photo)
    VALUES ($1, $2, $3, $4)
    RETURNING *
  `;
  const values = [first_name, last_name, email, photo];
  const { rows } = await db.query(query, values);
  return rows[0];
};

const updateTeacher = async (teacherId, teacherData) => {
  const { first_name, last_name, email, photo } = teacherData;
  const query = `
    UPDATE teacher
    SET first_name = $1, last_name = $2, email = $3, photo = $4
    WHERE teacher_id = $5
    RETURNING *
  `;
  const values = [first_name, last_name, email, photo, teacherId];
  const { rows } = await db.query(query, values);
  return rows[0];
};

const updateStudentFormTutor = async (studentId, formTutor) => {
  const query = `
    UPDATE students
    SET form_tutor = $1
    WHERE school_id = $2
    RETURNING *
  `;
  const { rows } = await db.query(query, [formTutor, studentId]);
  return rows[0];
};

module.exports = { 
  findAllTeachers, 
  findFormTutorByStudentId, 
  findTeacherById,
  createTeacher,
  updateTeacher,
  updateStudentFormTutor
};

