// Script para inserir dados fictícios de teste no banco
// Para rodar: npm run setup-db

require('dotenv').config();
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  ssl: { rejectUnauthorized: false }
});

async function seed() {
  try {
    // Create hashes for passwords
    const pwdParent1 = await bcrypt.hash('pwd123', 10);
    const pwdParent2 = await bcrypt.hash('pwd456', 10);
    const pwdAdmin1 = await bcrypt.hash('admin123', 10);
    const pwdAdmin2 = await bcrypt.hash('admin456', 10);

    // 1) Parents
    await pool.query(`
      INSERT INTO parents (first_name, last_name, mobile, email, adress_line, post_code, photo, password)
      VALUES
        ('<PERSON>', '<PERSON>', '11988887777', '<EMAIL>', 'Rua A, 123', '01234-567', null, $1),
        ('Bruno', 'Santos', '11999996666', '<EMAIL>', 'Av. B, 456', '07654-321', null, $2);
    `, [pwdParent1, pwdParent2]);

    // 2) Teachers
    await pool.query(`
      INSERT INTO teacher (first_name, last_name, email, photo, subjectID)
      VALUES
        ('Carlos', 'Oliveira', '<EMAIL>', NULL, 1),
        ('Daniela', 'Costa', '<EMAIL>', NULL, 2),
        ('Daniel', 'Rocha', '<EMAIL>', NULL, 3),
        ('Martin', 'Hale', '<EMAIL>', NULL, 4),
        ('Andrea', 'Lalovic', '<EMAIL>', NULL, 5),
        ('Florence', 'Cooper', '<EMAIL>', NULL, 6),
        ('Rob', 'Dilingham', '<EMAIL>', NULL, 7),
        ('Sarah', 'Davies', '<EMAIL>', NULL, 8),
        ('Oliver', 'Newman', '<EMAIL>', NULL, 9),
        ('Amanda', 'Willer', '<EMAIL>', NULL, 10),
        ('Maggie', 'Smith', '<EMAIL>', NULL, 11),
        ('Brian', 'Wilson', '<EMAIL>', NULL, 12),
        ('John', 'Thomas', '<EMAIL>', NULL, 13);
    `);

    // 3) Subjects
    await pool.query(`
      INSERT INTO subjects (Name)
      VALUES
        ('Mathematics'),
        ('Portuguese'),
        ('Science');
    `);

    // 4) Classes
    await pool.query(`
      INSERT INTO classes (SubjectID, teacher_ID, "class-code")
      VALUES
        (1, 1, 'L6-MaAISL-N'),
        (2, 2, 'L6-PoLiSL-N'),
        (3, 3, 'L6-BiSL-L'),
        (4, 4, 'L6-Reg-T(N)'),
        (5, 5, 'L6-EnLLHL-J'),
        (6, 6, 'L6-Tut-T(N)'),
        (7, 7, 'L6-FmHL'),
        (8, 8, 'L6UnP'),
        (9, 9, 'L6-GPolHL-3'),
        (10, 10, 'L6-ToK-L'),
        (11, 11, 'L6-PS-2'),
        (12, 12, 'L6-PSHE-T(N)'),
        (13, 13, 'L6-PE-1');
    `);

    // 5) Students
    await pool.query(`
      INSERT INTO students (person_ID, first_name, last_name, photo, notifications, house, year_ID, form_tutor)
      VALUES
        (1, 'Lucas', 'Pereira', NULL, FALSE, 'York', 2, 'Mr Smith'),
        (2, 'Mariana', 'Almeida', NULL, FALSE, 'Tudor', 2, 'Mr Smith'),
        (3, 'Luisa', 'Santos', NULL, FALSE, 'Lancastes', 1, 'Mr Smith');
    `);

    // 6) Years
    await pool.query(`
      INSERT INTO year (year_code)
      VALUES
        ('U6'),
        ('L6');
    `);

    // 7) Timetable
    await pool.query(`
      INSERT INTO timetable (School_ID, Week_day, start_time, room, timetable_cycle, class_ID, teacher_ID, subject_ID)
      VALUES
        (1, 'Mon', '08:00', '501', 'Week A', 1, 1, 1);
    `);

    // 8) Events
    await pool.query(`
      INSERT INTO events (title, description, event_date, time, photo, year_ID)
      VALUES
        ('Football Match', 'This is a draft description.', '2025-12-06', '15:30', null, null),
        ('Parent Meeting', 'This is a draft description', '2025-07-12', '17:00', null, null),
        ('Photo shoot', 'This is a draft description', '2025-04-23', '08:00', null, null),
        ('Volleyball Match', 'This is a draft description', '2025-08-30', '15:00', null, null),
        ('Teachers Meeting', 'This is a draft description', '2025-11-27', '19:00', null, null);
    `);

    // 9) Reports
    await pool.query(`
      INSERT INTO reports (School_ID, Term, School_year, pdf_path)
      VALUES
        (1, '3rd Term', '2025', '/reports/report1.pdf'),
        (2, '5th Term', '2025', '/reports/report2.pdf');
    `);

    // 10) Medical Info
    await pool.query(`
      INSERT INTO medical_info (School_ID, medical_notes, medical_contact)
      VALUES
        (1, 'No medical restrictions', 'Dr. Souza: +55 11 97777-8888'),
        (2, 'Peanut allergy', 'Dr. Lima: +55 11 96666-5555');
    `);

    // 11) Assignments
    await pool.query(`
      INSERT INTO assignments (School_ID, description, due_date, Teams_link, subject_id, completed)
      VALUES
        (1, 'Book pages 19-20', '2025-09-19', 'https://teams.link/math', 1, false),
        (2, 'List of verbs', '2025-06-10', 'https://teams.link/port', 2, false),
        (2, 'Human body list', '2025-03-25', 'https://teams.link/bio', 3, true),
        (1, 'Exercise list', '2025-08-08', 'https://teams.link/GP', 9, true);
    `);

    // 12) Administrators
    await pool.query(`
      INSERT INTO administrators (first_name, last_name, email, password)
      VALUES
        ('João', 'Castro', '<EMAIL>', $1),
        ('Laura', 'Fernandes', '<EMAIL>', $2);
    `, [pwdAdmin1, pwdAdmin2]);

    console.log('Dados fictícios inseridos com sucesso!');
  } catch (err) {
    console.error('Erro ao inserir dados fictícios:', err);
  } finally {
    await pool.end();
  }
}

seed();
