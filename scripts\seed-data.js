// Script para inserir dados fictícios de teste no banco
// Para rodar: npm run setup-db

require('dotenv').config();
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  ssl: { rejectUnauthorized: false }
});

async function seed() {
  try {
    // Create hashes for passwords
    const pwdParent1 = await bcrypt.hash('pwd123', 10);
    const pwdParent2 = await bcrypt.hash('pwd456', 10);
    const pwdAdmin1 = await bcrypt.hash('admin123', 10);
    const pwdAdmin2 = await bcrypt.hash('admin456', 10);

    // 1) Parents
    await pool.query(`
      INSERT INTO parents (first_name, last_name, mobile, email, adress_line, post_code, photo, password)
      VALUES
        ('<PERSON>', '<PERSON>', '11988887777', '<EMAIL>', 'Rua A, 123', '01234-567', null, $1),
        ('Bruno', 'Santos', '11999996666', '<EMAIL>', 'Av. B, 456', '07654-321', null, $2);
    `, [pwdParent1, pwdParent2]);

    // 2) Teachers
    await pool.query(`
      INSERT INTO teacher (first_name, last_name, email)
      VALUES
        ('Carlos', 'Oliveira', '<EMAIL>'),
        ('Daniela', 'Costa', '<EMAIL>');
    `);

    // 3) Subjects
    await pool.query(`
      INSERT INTO subjects (Name, Code)
      VALUES
        ('Mathematics', 'MAT'),
        ('Portuguese', 'POR'),
        ('Science', 'SCI');
    `);

    // 4) Classes
    await pool.query(`
      INSERT INTO classes (year_code, SubjectID, teacher_ID)
      VALUES
        ('U6', 1, 1),
        ('L6', 2, 2);
    `);

    // 5) Students
    await pool.query(`
      INSERT INTO students (person_ID, class_ID, first_name, last_name, year_code, form_tutor, photo, Notifications)
      VALUES
        (1, 1, 'Lucas', 'Pereira', 'U6', 'Prof. Silva', null, null),
        (2, 2, 'Mariana', 'Almeida', 'L6', 'Prof. Costa', null, null);
    `);

    // 6) Years
    await pool.query(`
      INSERT INTO year (year_code, school_ID)
      VALUES
        ('U6', 1),
        ('L6', 2);
    `);

    // 7) Timetable
    await pool.query(`
      INSERT INTO timetable (School_ID, Week_day, start_time, end_time, room, Timetable_cycle, class_ID)
      VALUES
        (1, 'Monday', '08:00', '09:00', 'Room 101', 'A', 1),
        (2, 'Tuesday', '09:00', '10:00', 'Room 102', 'B', 2);
    `);

    // 8) Events
    await pool.query(`
      INSERT INTO events (title, description, event_date, time, photo, year_ID)
      VALUES
        ('Science Fair', 'Project presentations by students', '2025-10-15', '14:00', null, 1),
        ('Parent Meeting', 'Bimonthly meeting with parents', '2025-11-01', '18:30', null, 1);
    `);

    // 9) Reports
    await pool.query(`
      INSERT INTO reports (School_ID, Term, School_year, pdf_path)
      VALUES
        (1, '3rd Term', '2025', '/reports/report1.pdf'),
        (2, '5th Term', '2025', '/reports/report2.pdf');
    `);

    // 10) Medical Info
    await pool.query(`
      INSERT INTO medical_info (School_ID, medical_notes, medical_contact)
      VALUES
        (1, 'No medical restrictions', 'Dr. Souza: +55 11 97777-8888'),
        (2, 'Peanut allergy', 'Dr. Lima: +55 11 96666-5555');
    `);

    // 11) Assignments
    await pool.query(`
      INSERT INTO assignments (School_ID, subject_id, description, due_date, Teams_link)
      VALUES
        (1, 1, 'Book pages 19-20', '2025-10-20', 'https://teams.link/math'),
        (2, 2, 'Exercise list', '2025-10-22', 'https://teams.link/port');
    `);

    // 12) Administrators
    await pool.query(`
      INSERT INTO administrators (first_name, last_name, email, password)
      VALUES
        ('João', 'Castro', '<EMAIL>', $1),
        ('Laura', 'Fernandes', '<EMAIL>', $2);
    `, [pwdAdmin1, pwdAdmin2]);

    console.log('Dados fictícios inseridos com sucesso!');
  } catch (err) {
    console.error('Erro ao inserir dados fictícios:', err);
  } finally {
    await pool.end();
  }
}

seed();
