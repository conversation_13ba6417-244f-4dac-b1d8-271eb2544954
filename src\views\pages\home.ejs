<header class="main-header">
  <div class="header-content">
    <h1>St. Paul's School Portal</h1>
    <nav>
      <ul>
        <li><a href="/home" class="active">Home</a></li>
        <li><a href="/timetable">Timetable</a></li>
        <li><a href="/reports">Reports</a></li>
        <li><a href="/assignments">Assignments</a></li>
        <li><a href="/profile">Profile</a></li>
      </ul>
    </nav>
    <div class="user-info">
      <span>Welcome, <%= userName %></span>
      <a href="/logout">Logout</a>
    </div>
  </div>
</header>

<main class="home-content">
  <section class="welcome-section">
    <h2>Welcome to St. Paul's School Portal</h2>
    <p>Stay updated with the latest school events and access the academic calendar.</p>
    
    <div class="calendar-section">
      <h3>School Calendar</h3>
      <a href="<%= calendarPath %>" target="_blank" class="calendar-link">
        <div class="calendar-preview">
          <img src="/images/calendar-icon.png" alt="Calendar Icon">
          <span>View 2025 Academic Calendar</span>
        </div>
      </a>
    </div>
  </section>

  <section class="events-section">
    <h3>Upcoming Events</h3>
    
    <% if (events.length === 0) { %>
      <p class="no-events">No upcoming events scheduled.</p>
    <% } else { %>
      <div class="events-grid">
        <% events.forEach(event => { %>
          <div class="event-card">
            <% if (event.photo) { %>
              <div class="event-image">
                <img src="<%= event.photo %>" alt="<%= event.title %>">
              </div>
            <% } %>
            <div class="event-details">
              <h4><%= event.title %></h4>
              <p class="event-date">
                <%= new Date(event.event_date).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) %> 
                at <%= event.time.substring(0, 5) %>
              </p>
              <p class="event-description"><%= event.description %></p>
            </div>
          </div>
        <% }) %>
      </div>
    <% } %>
  </section>

  <% if (userRole === 'admin') { %>
    <section class="admin-controls">
      <h3>Event Management</h3>
      <a href="/events/add" class="admin-button">Add New Event</a>
    </section>
  <% } %>
</main>