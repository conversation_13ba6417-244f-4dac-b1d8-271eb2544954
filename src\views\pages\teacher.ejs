<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/css/styles.css">
</head>
<body>
  <header>
    <h1>Portal de Pais - St. Paul's School</h1>
    <p>Bem-vindo(a), <%= user %>!</p>
  </header>

  <main>
    <div class="container">
      <h2>Professores</h2>
      
      <% if (role === 'admin') { %>
        <div class="admin-controls">
          <a href="/teachers/new" class="btn btn-primary">Adicionar Professor</a>
        </div>
      <% } %>
      
      <% if (studentId && formTutor) { %>
        <div class="form-tutor-section">
          <h3>Form Tutor do Aluno</h3>
          <div class="teacher-card highlight">
            <p><strong>Nome:</strong> <%= formTutor %></p>
            
            <% if (role === 'admin') { %>
              <button class="btn btn-secondary" onclick="showTutorForm()">Editar Tutor</button>
              
              <div id="tutor-form" style="display: none;">
                <form action="/teachers/update-tutor" method="POST">
                  <input type="hidden" name="studentId" value="<%= studentId %>">
                  <div class="form-group">
                    <label for="formTutor">Nome do Tutor:</label>
                    <input type="text" id="formTutor" name="formTutor" value="<%= formTutor %>" required>
                  </div>
                  <button type="submit" class="btn btn-primary">Salvar</button>
                  <button type="button" class="btn btn-secondary" onclick="hideTutorForm()">Cancelar</button>
                </form>
              </div>
            <% } %>
          </div>
        </div>
      <% } %>

      <div class="teachers-list">
        <h3>Todos os Professores</h3>
        <% if (teachers.length === 0) { %>
          <p>Nenhum professor encontrado.</p>
        <% } else { %>
          <div class="teachers-grid">
            <% teachers.forEach(teacher => { %>
              <div class="teacher-card">
                <% if (teacher.photo) { %>
                  <img src="<%= teacher.photo %>" alt="Foto de <%= teacher.first_name %> <%= teacher.last_name %>">
                <% } else { %>
                  <div class="no-photo">Sem foto</div>
                <% } %>
                <h4><%= teacher.first_name %> <%= teacher.last_name %></h4>
                <p><strong>Email:</strong> <%= teacher.email %></p>
                
                <% if (role === 'admin') { %>
                  <div class="admin-actions">
                    <a href="/teachers/edit/<%= teacher.teacher_id %>" class="btn btn-sm btn-primary">Editar</a>
                  </div>
                <% } %>
              </div>
            <% }) %>
          </div>
        <% } %>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2024 St. Paul's School - Portal de Pais</p>
  </footer>
</body>
</html>

<script>
  function showTutorForm() {
    document.getElementById('tutor-form').style.display = 'block';
  }
  
  function hideTutorForm() {
    document.getElementById('tutor-form').style.display = 'none';
  }
</script>


