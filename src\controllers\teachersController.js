const teacherModel = require('../models/teacherModel');

const renderTeacher = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    const studentId = req.session.studentId;

    console.log('Usu<PERSON>rio logado (ID):', userId);
    console.log('Função:', userRole);
    console.log('<PERSON>uno selecionado:', studentId);

    const teachers = await teacherModel.findAllTeachers();
    console.log('Teachers found:', teachers.length);
    
    let formTutor = null;
    if (studentId) {
      formTutor = await teacherModel.findFormTutorByStudentId(studentId);
      console.log('Form tutor for student', studentId, ':', formTutor);
    }

    res.render('pages/teacher', {
      teachers,
      formTutor,
      user: userName,
      role: userRole,
      title: 'Professores',
      studentId
    });

  } catch (error) {
    console.error('Erro ao carregar professores:', error); 
    res.status(500).send('Erro ao carregar professores.');
  }
};

const renderTeacherForm = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    let teacher = { first_name: '', last_name: '', email: '', photo: '' };
    const teacherId = req.params.id;
    
    if (teacherId) {
      teacher = await teacherModel.findTeacherById(teacherId);
      if (!teacher) {
        return res.status(404).send('Professor não encontrado');
      }
    }
    
    res.render('pages/teacherForm', {
      teacher,
      user: userName,
      role: userRole,
      title: teacherId ? 'Editar Professor' : 'Adicionar Professor',
      isEdit: !!teacherId
    });
    
  } catch (error) {
    console.error('Erro ao carregar formulário de professor:', error);
    res.status(500).send('Erro ao carregar formulário de professor.');
  }
};

const createOrUpdateTeacher = async (req, res) => {
  try {
    const userRole = req.session.userRole;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).send('Acesso negado');
    }
    
    const teacherId = req.params.id;
    const { first_name, last_name, email, photo } = req.body;
    
    const teacherData = { first_name, last_name, email, photo };
    
    if (teacherId) {
      // Atualizar professor existente
      await teacherModel.updateTeacher(teacherId, teacherData);
    } else {
      // Criar novo professor
      await teacherModel.createTeacher(teacherData);
    }
    
    res.redirect('/teachers');
    
  } catch (error) {
    console.error('Erro ao salvar professor:', error);
    res.status(500).send('Erro ao salvar professor.');
  }
};

const updateFormTutor = async (req, res) => {
  try {
    const userRole = req.session.userRole;
    
    // Verificar se é administrador
    if (userRole !== 'admin') {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const { studentId, formTutor } = req.body;
    
    if (!studentId) {
      return res.status(400).json({ error: 'ID do aluno é obrigatório' });
    }
    
    await teacherModel.updateStudentFormTutor(studentId, formTutor);
    
    res.redirect('/teachers');
    
  } catch (error) {
    console.error('Erro ao atualizar form tutor:', error);
    res.status(500).json({ error: 'Erro ao atualizar form tutor' });
  }
};

module.exports = { 
  renderTeacher,
  renderTeacherForm,
  createOrUpdateTeacher,
  updateFormTutor
};
