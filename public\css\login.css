/* public/css/login.css */

/* Reset básico para margens, paddings e box-sizing */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Body ocupa toda a tela e não gera scroll */
body {
  font-family: 'Roboto Slab', serif;
  background-color: #ffffff;
  height: 100vh;
  overflow: hidden; /* Evita scroll geral */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Container principal: divide em duas colunas, cada uma com 50% da largura */
.login-container {
  display: flex;
  width: 100%;
  height: 100vh; /* garante 100% da altura da viewport */
}

/* Lado esquerdo: formulário */
.login-form-wrapper {
  flex: 1; /* ocupa 50% da largura */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  overflow: auto; /* permite scroll interno se necessário */
}

/* Logo centralizado (imagem + texto embutido) */
.login-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo img {
  width: 326px;  /* ajuste conforme a proporção da sua logo */
  height: 268px;
}

/* Formulário */
.login-form {
  width: 100%;
  max-width: 400px;
}

.login-form label {
  display: block;
  font-size: 1rem;
  color: #001f3f; /* Azul escuro */
  margin-bottom: 0.5rem;
}

.login-form input[type="email"],
.login-form input[type="password"] {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  border: 2px solid #001f3f;
  border-radius: 20px;
  background-color: #f0f0f0;
  margin-bottom: 1.5rem;
  outline: none;
  transition: border-color 0.2s;
}

.login-form input[type="email"]:focus,
.login-form input[type="password"]:focus {
  border-color: #800020; /* Bordô quando em foco */
}

/* Botão de login */
.login-form button[type="submit"] {
  width: 240px;
  padding: 0.75rem;
  font-size: 1.25rem;
  color: #ffffff;
  background-color: #800020; /* Bordô */
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: 'Roboto Slab', serif;


  display: block;
  margin: 0 auto;   
}

.login-form button[type="submit"]:hover {
  background-color: #99002e; /* Tom mais claro no hover */
}

/* Mensagem de erro */
.login-form .error-message {
  color: #c0392b; /* Vermelho escuro */
  font-size: 0.875rem;
  margin-top: 0.5rem;
  text-align: center;
}

/* Lado direito: imagem ocupa totalmente a metade direita */
.login-image-wrapper {
  flex: 1; /* ocupa 50% da largura */
  background: url('/images/login-side-image.jpg') no-repeat center center;
  background-size: cover;
  margin-bottom: 60px;
}

/* Responsividade: em telas menores que 768px, empilha verticalmente e esconde a imagem */
@media (max-width: 768px) {
  body {
    align-items: flex-start;
  }

  .login-container {
    flex-direction: column;
    height: auto; /* permite que o conteúdo cresça */
  }

  .login-image-wrapper {
    display: none; /* Oculta a coluna de imagem em telas pequenas */
  }
}