const profileModel = require('../models/profileModel');
const studentModel = require('../models/studentModel');

const profileController = {
  // Renderizar a página de perfil
  renderProfile: async (req, res) => {
    try {
      const userId = req.session.userId;
      const userRole = req.session.userRole;
      const userName = req.session.userName;
      const studentId = req.session.studentId;
      
      // Buscar informações do pai
      const parentInfo = await profileModel.getParentById(userId);
      
      // Buscar informações do estudante selecionado
      let studentInfo = null;
      let medicalInfo = null;
      
      if (studentId) {
        studentInfo = await studentModel.findStudentById(studentId);
        medicalInfo = await profileModel.getMedicalInfo(studentId);
      }
      
      res.render('pages/profile', {
        title: 'Perfil',
        user: userName,
        role: userRole,
        parentInfo,
        studentInfo,
        medicalInfo: medicalInfo || { medical_notes: '' },
        studentId
      });
      
    } catch (error) {
      console.error('Erro ao carregar perfil:', error);
      res.status(500).send('Erro ao carregar perfil.');
    }
  },
  
  // Atualizar informações do perfil
  updateProfile: async (req, res) => {
    try {
      const userId = req.session.userId;
      const userRole = req.session.userRole;
      const studentId = req.session.studentId;
      
      // Verificar se é um pai
      if (userRole !== 'parent') {
        return res.status(403).send('Acesso negado');
      }
      
      // Verificar qual tipo de atualização está sendo feita
      const isParentInfoUpdate = req.body.first_name !== undefined;
      const isMedicalInfoUpdate = req.body.medical_notes !== undefined;
      
      // Atualizar informações do pai
      if (isParentInfoUpdate) {
        const parentData = {
          first_name: req.body.first_name,
          last_name: req.body.last_name,
          mobile: req.body.mobile,
          email: req.body.email,
          adress_line: req.body.adress_line,
          post_code: req.body.post_code,
          photo: req.body.photo
        };
        
        await profileModel.updateParent(userId, parentData);
        
        // Atualizar o nome do usuário na sessão
        req.session.userName = `${parentData.first_name} ${parentData.last_name}`;
      }
      
      // Atualizar informações médicas do estudante, se houver um estudante selecionado
      if (isMedicalInfoUpdate && studentId) {
        const medicalData = {
          medical_notes: req.body.medical_notes
        };
        
        await profileModel.updateMedicalInfo(studentId, medicalData);
      }
      
      res.redirect('/profile');
      
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      res.status(500).send('Erro ao atualizar perfil.');
    }
  }
};

module.exports = profileController;
