const db = require('../config/db');

const timetableModel = {
    async getAll() {
        const result = await db.query(`
            SELECT t.timetable_id, t.week_day, t.start_time, t.room, t.timetable_cycle,
                   s.Name as subject_name, 
                   tc.first_name as teacher_first_name, tc.last_name as teacher_last_name,
                   c."class-code" as class_code
            FROM timetable t
            LEFT JOIN subjects s ON t.subject_id = s.subject_id
            LEFT JOIN teacher tc ON t.teacher_id = tc.teacher_id
            LEFT JOIN classes c ON t.class_id = c.class_id
            ORDER BY t.week_day, t.start_time
        `);
        
        return result.rows;
    },

    async getAllForParent(parentId) {
        const result = await db.query(`
            SELECT t.timetable_id, t.week_day, t.start_time, t.room, t.timetable_cycle,
                   s.Name as subject_name, 
                   tc.first_name as teacher_first_name, tc.last_name as teacher_last_name,
                   CONCAT(st.first_name, ' ', st.last_name) as student_name,
                   c."class-code" as class_code
            FROM timetable t
            LEFT JOIN subjects s ON t.subject_id = s.subject_id
            LEFT JOIN teacher tc ON t.teacher_id = tc.teacher_id
            LEFT JOIN students st ON t.school_id = st.school_id
            LEFT JOIN classes c ON t.class_id = c.class_id
            WHERE st.person_id = $1
            ORDER BY t.week_day, t.start_time
        `, [parentId]);
        
        return result.rows;
    },

    async getByStudentId(studentId) {
        const result = await db.query(`
            SELECT t.timetable_id, t.week_day, t.start_time, t.room, t.timetable_cycle,
                   s.Name as subject_name, 
                   tc.first_name as teacher_first_name, tc.last_name as teacher_last_name,
                   c."class-code" as class_code
            FROM timetable t
            LEFT JOIN subjects s ON t.subject_id = s.subject_id
            LEFT JOIN teacher tc ON t.teacher_id = tc.teacher_id
            LEFT JOIN classes c ON t.class_id = c.class_id
            WHERE t.school_id = $1
            ORDER BY t.week_day, t.start_time
        `, [studentId]);
        
        return result.rows;
    },

    async getById(id) {
        const result = await db.query(`
            SELECT t.*, s.Name as subject_name, 
                   tc.first_name as teacher_first_name, tc.last_name as teacher_last_name,
                   c.class_code
            FROM timetable t
            LEFT JOIN subjects s ON t.subject_ID = s.subject_ID
            LEFT JOIN teacher tc ON t.teacher_ID = tc.teacher_ID
            LEFT JOIN classes c ON t.class_ID = c.class_ID
            WHERE t.Timetable_ID = $1
        `, [id]);
        return result.rows[0];
    },

    async create(timetable) {
        const { School_ID, Week_day, start_time, end_time, room, timetable_cycle, class_ID, teacher_ID, subject_ID } = timetable;
        const result = await db.query(
            `INSERT INTO timetable 
             (School_ID, Week_day, start_time, end_time, room, timetable_cycle, class_ID, teacher_ID, subject_ID)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *`,
            [School_ID, Week_day, start_time, end_time, room, timetable_cycle, class_ID, teacher_ID, subject_ID]
        );
        return result.rows[0];
    },

    async update(id, timetable) {
        const { School_ID, Week_day, start_time, end_time, room, timetable_cycle, class_ID, teacher_ID, subject_ID } = timetable;
        const result = await db.query(
            `UPDATE timetable 
             SET School_ID = $1, Week_day = $2, start_time = $3, end_time = $4,
                 room = $5, timetable_cycle = $6, class_ID = $7, teacher_ID = $8, subject_ID = $9
             WHERE Timetable_ID = $10 RETURNING *`,
            [School_ID, Week_day, start_time, end_time, room, timetable_cycle, class_ID, teacher_ID, subject_ID, id]
        );
        return result.rows[0];
    },

    async delete(id) {
        await db.query(`DELETE FROM timetable WHERE Timetable_ID = $1`, [id]);
        return { message: 'Timetable entry deleted successfully' };
    }
};

module.exports = timetableModel;
