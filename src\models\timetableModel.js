const db = require('../config/db');

const timetableModel = {
    async getAll() {
        const result = await db.query(
            `SELECT t.*, 
                    s.first_name || ' ' || s.last_name AS student_name,
                    c.class_ID, 
                    sub.name AS subject,
                    te.first_name || ' ' || te.last_name AS teacher
             FROM timetable t
             LEFT JOIN students s ON t.School_ID = s.School_ID
             LEFT JOIN classes c ON t.class_ID = c.class_ID
             LEFT JOIN subjects sub ON c.SubjectID = sub.Subject_ID
             LEFT JOIN teacher te ON c.teacher_ID = te.teacher_ID`
        );
        return result.rows;
    },

    async getById(id) {
        const result = await db.query(
            `SELECT * FROM timetable WHERE Timetable_ID = $1`,
            [id]
        );
        return result.rows[0];
    },

    async create(timetable) {
        const { School_ID, Week_day, start_time, end_time, room, Timetable_cycle, class_ID } = timetable;
        const result = await db.query(
            `INSERT INTO timetable (School_ID, Week_day, start_time, end_time, room, Timetable_cycle, class_ID)
             VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
            [School_ID, Week_day, start_time, end_time, room, Timetable_cycle, class_ID]
        );
        return result.rows[0];
    },

    async update(id, timetable) {
        const { School_ID, Week_day, start_time, end_time, room, Timetable_cycle, class_ID } = timetable;
        const result = await db.query(
            `UPDATE timetable 
             SET School_ID = $1, Week_day = $2, start_time = $3, end_time = $4,
                 room = $5, Timetable_cycle = $6, class_ID = $7
             WHERE Timetable_ID = $8 RETURNING *`,
            [School_ID, Week_day, start_time, end_time, room, Timetable_cycle, class_ID, id]
        );
        return result.rows[0];
    },

    async delete(id) {
        await db.query(`DELETE FROM timetable WHERE Timetable_ID = $1`, [id]);
        return { message: 'Timetable entry deleted successfully' };
    }
};

module.exports = timetableModel;
