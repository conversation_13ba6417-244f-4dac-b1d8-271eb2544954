<div class="container">
  <h2><%= isEdit ? 'Editar Tarefa' : 'Adicionar <PERSON>' %></h2>
  
  <div class="form-container">
    <form action="/assignments/save<%= isEdit ? '/' + assignment.assignmentid : '' %>" method="POST">
      <div class="form-group">
        <label for="School_ID">Aluno:</label>
        <select id="School_ID" name="School_ID" required>
          <option value="">Selecione um aluno</option>
          <% students.forEach(student => { %>
            <option value="<%= student.school_id %>" <%= assignment.School_ID == student.school_id ? 'selected' : '' %>>
              <%= student.first_name %> <%= student.last_name %>
            </option>
          <% }) %>
        </select>
      </div>
      
      <div class="form-group">
        <label for="subject_id">Subject:</label>
        <select id="subject_id" name="subject_id" required>
          <option value="">Selecione uma disciplina</option>
          <% subjects.forEach(subject => { %>
            <option value="<%= subject.subject_id %>" <%= assignment.subject_id == subject.subject_id ? 'selected' : '' %>>
              <%= subject.name %>
            </option>
          <% }) %>
        </select>
      </div>
      
      <div class="form-group">
        <label for="description">Description:</label>
        <textarea id="description" name="description" rows="4"><%= assignment.description %></textarea>
      </div>
      
      <div class="form-group">
        <label for="due_date">Due Date:</label>
        <input type="date" id="due_date" name="due_date" value="<%= assignment.due_date ? new Date(assignment.due_date).toISOString().split('T')[0] : '' %>" required>
      </div>
      
      <div class="form-group">
        <label for="Teams_link">Link do Teams:</label>
        <input type="url" id="Teams_link" name="Teams_link" value="<%= assignment.Teams_link %>">
      </div>

      <div class="form-group">
        <label for="completed">Status:</label>
        <select id="completed" name="completed">
          <option value="false" <%= assignment.completed === false ? 'selected' : '' %>>Pendente</option>
          <option value="true" <%= assignment.completed === true ? 'selected' : '' %>>Concluída</option>
        </select>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">Salvar</button>
        <a href="/assignments" class="btn btn-secondary">Cancelar</a>
      </div>
    </form>
  </div>
</div>
