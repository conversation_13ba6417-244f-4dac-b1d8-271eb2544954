const studentModel = require('../models/studentModel');
const yearModel = require('../models/yearModel');

const renderStudentSelectionPage = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;

    console.log("Usuário logado (ID):", userId);
    console.log("Função:", userRole);

    let students = [];
    let years = []; // Se yearModel estiver implementado

    if (userRole === "admin") {
      // Admin pode ver todos os alunos
      students = await studentModel.getAll(); // Certifique-se de que esta função existe
    } else if (userRole === "parent" && userId) {
      // Pais veem apenas seus filhos
      students = await studentModel.getByParentId(userId); // Usando o nome correto da função
    } else {
      // Qualquer outro papel ou ausência de ID não retorna alunos
      console.warn("Sessão inválida ou não autorizada");
    }

    res.render("pages/studentSelection", {
      students,
      years,
      user: userName,
      role: userRole,
      title: "Seleção de Alunos",
    });

  } catch (error) {
    console.error("Erro ao carregar alunos:", error);
    res.status(500).send("Erro ao carregar alunos.");
  }
};

const selectStudent = (req, res, next) => {
  const studentId = parseInt(req.body.studentId, 10);
  if (!studentId) {
    return res.status(400).send("Aluno não selecionado");
  }

  console.log(">> selectStudent, setting session.studentId =", studentId);
  req.session.studentId = studentId;
  req.session.save((err) => {
    if (err) return next(err);
    res.redirect("/home");
  });
};

module.exports = {
  renderStudentSelectionPage,
  selectStudent
};
