const db = require('../config/db');

const assignmentModel = {
    // Função para buscar todas as tarefas
    async getAll() {
        const result = await db.query(`
            SELECT a.*, s.Name as subject_name
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.subject_ID
            ORDER BY a.due_date
        `);
        return result.rows;
    },

    // Função para buscar tarefas por ID do aluno
    async getByStudentId(studentId) {
        const result = await db.query(`
            SELECT a.*, s.Name as subject_name
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.subject_ID
            WHERE a.School_ID = $1
            ORDER BY a.due_date
        `, [studentId]);
        return result.rows;
    },

    // Função para buscar uma tarefa por ID
    async getById(id) {
        const result = await db.query(`
            SELECT a.*, s.Name as subject_name
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.subject_ID
            WHERE a.assignmentID = $1
        `, [id]);
        return result.rows[0];
    },

    // Função para criar uma nova tarefa
    async create(assignment) {
        const { School_ID, description, due_date, Teams_link, subject_id, completed } = assignment;
        const result = await db.query(
            `INSERT INTO assignments 
             (School_ID, description, due_date, Teams_link, subject_id, completed)
             VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
            [School_ID, description, due_date, Teams_link, subject_id, completed]
        );
        return result.rows[0];
    },

    // Função para atualizar uma tarefa existente
    async update(id, assignment) {
        const { description, due_date, Teams_link, subject_id, completed } = assignment;
        const result = await db.query(
            `UPDATE assignments 
             SET description = $1, due_date = $2, Teams_link = $3, 
                 subject_id = $4, completed = $5
             WHERE assignmentID = $6 RETURNING *`,
            [description, due_date, Teams_link, subject_id, completed, id]
        );
        return result.rows[0];
    },

    // Função para alternar o status de conclusão de uma tarefa
    async toggleCompleted(id) {
        const result = await db.query(
            `UPDATE assignments 
             SET completed = NOT completed
             WHERE assignmentID = $1 RETURNING *`,
            [id]
        );
        return result.rows[0];
    },

    // Função para excluir uma tarefa
    async delete(id) {
        await db.query(`DELETE FROM assignments WHERE assignmentID = $1`, [id]);
        return { message: 'Assignment deleted successfully' };
    }
};

module.exports = assignmentModel;
