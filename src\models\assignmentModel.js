const db = require('../config/db');

const findAllAssignments = async () => {
  const query = `
    SELECT a.*, s.first_name || ' ' || s.last_name AS student_name, 
           sub.Name AS subject_name
    FROM assignments a
    LEFT JOIN students s ON a.School_ID = s.School_ID
    LEFT JOIN subjects sub ON a.subject_id = sub.Subject_ID
    ORDER BY a.due_date ASC
  `;
  const { rows } = await db.query(query);
  return rows;
};

const findAssignmentsByStudentId = async (studentId) => {
  console.log('Model: Buscando tarefas para o aluno ID:', studentId);
  const query = `
    SELECT a.*, s.first_name || ' ' || s.last_name AS student_name,
           sub.Name AS subject_name
    FROM assignments a
    LEFT JOIN students s ON a.School_ID = s.School_ID
    LEFT JOIN subjects sub ON a.subject_id = sub.Subject_ID
    WHERE a.School_ID = $1
    ORDER BY a.due_date ASC
  `;
  const { rows } = await db.query(query, [studentId]);
  console.log('Model: Tarefas encontradas:', rows.length);
  return rows;
};

const findAssignmentById = async (assignmentId) => {
  const query = `
    SELECT a.*, s.first_name || ' ' || s.last_name AS student_name,
           sub.Name AS subject_name
    FROM assignments a
    LEFT JOIN students s ON a.School_ID = s.School_ID
    LEFT JOIN subjects sub ON a.subject_id = sub.Subject_ID
    WHERE a.assignmentID = $1
  `;
  const { rows } = await db.query(query, [assignmentId]);
  return rows.length > 0 ? rows[0] : null;
};

const createAssignment = async (assignmentData) => {
  const { School_ID, subject_id, description, due_date, Teams_link } = assignmentData;
  const query = `
    INSERT INTO assignments (School_ID, subject_id, description, due_date, Teams_link)
    VALUES ($1, $2, $3, $4, $5)
    RETURNING *
  `;
  const values = [School_ID, subject_id, description, due_date, Teams_link];
  const { rows } = await db.query(query, values);
  return rows[0];
};

const updateAssignment = async (assignmentId, assignmentData) => {
  const { School_ID, subject_id, description, due_date, Teams_link } = assignmentData;
  const query = `
    UPDATE assignments
    SET School_ID = $1, subject_id = $2, description = $3, due_date = $4, Teams_link = $5
    WHERE assignmentID = $6
    RETURNING *
  `;
  const values = [School_ID, subject_id, description, due_date, Teams_link, assignmentId];
  const { rows } = await db.query(query, values);
  return rows[0];
};

const deleteAssignment = async (assignmentId) => {
  const query = 'DELETE FROM assignments WHERE assignmentID = $1 RETURNING *';
  const { rows } = await db.query(query, [assignmentId]);
  return rows[0];
};

module.exports = {
  findAllAssignments,
  findAssignmentsByStudentId,
  findAssignmentById,
  createAssignment,
  updateAssignment,
  deleteAssignment
};