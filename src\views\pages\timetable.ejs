<h1>School Timetable</h1>

<!-- Botão de adicionar novo horário -->
<form action="/timetable" method="POST" style="margin-bottom: 20px;">
  <h3>Add New Entry</h3>
  <input type="text" name="School_ID" placeholder="School ID" required />
  <input type="text" name="Week_day" placeholder="Day" required />
  <input type="text" name="Timetable_cycle" placeholder="Cycle" required />
  <input type="text" name="class_ID" placeholder="Class ID" required />
  <input type="time" name="start_time" required />
  <input type="time" name="end_time" required />
  <input type="text" name="room" placeholder="Room" required />
  <button type="submit">Add Entry</button>
</form>

<table border="1" cellspacing="0" cellpadding="8">
  <thead>
    <tr>
      <th>Student</th>
      <th>Day</th>
      <th>Cycle</th>
      <th>Start</th>
      <th>End</th>
      <th>Subject</th>
      <th>Teacher</th>
      <th>Room</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <% if (timetable && timetable.length > 0) { %>
      <% timetable.forEach(row => { %>
        <tr>
          <td><%= row.student_name || '-' %></td>
          <td><%= row.week_day %></td>
          <td><%= row.timetable_cycle %></td>
          <td><%= row.start_time %></td>
          <td><%= row.end_time %></td>
          <td><%= row.subject %></td>
          <td><%= row.teacher %></td>
          <td><%= row.room %></td>
          <td>
            <!-- Botão de deletar -->
            <form action="/timetable/<%= row.timetable_id %>/delete" method="POST" style="display:inline;">
              <button type="submit" onclick="return confirm('Are you sure you want to delete this entry?')">Delete</button>
            </form>

            <!-- Botão de editar (form simplificado para PUT) -->
            <a href="/timetable/<%= row.timetable_id %>/edit">
              <button type="button">Edit</button>
            </a>
          </td>
        </tr>
      <% }) %>
    <% } else { %>
      <tr>
        <td colspan="9" style="text-align: center;">No timetable entries found.</td>
      </tr>
    <% } %>
  </tbody>
</table>
