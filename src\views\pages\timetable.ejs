<h1>School Timetable</h1>

<!-- Botão de adicionar novo hor<PERSON> (apenas para administradores) -->
<% if (userRole === 'admin') { %>
  <form action="/timetable" method="POST" style="margin-bottom: 20px;">
    <h3>Add New Entry</h3>
    <input type="text" name="School_ID" placeholder="School ID" required />
    <input type="text" name="Week_day" placeholder="Day" required />
    <input type="text" name="Timetable_cycle" placeholder="Cycle" required />
    <input type="text" name="class_ID" placeholder="Class ID" required />
    <input type="time" name="start_time" required />
    <input type="time" name="end_time" required />
    <input type="text" name="room" placeholder="Room" required />
    <button type="submit">Add Entry</button>
  </form>
<% } %>

<table border="1" cellspacing="0" cellpadding="8">
  <thead>
    <tr>
      <th>Day</th>
      <th>Cycle</th>
      <th>Start</th>
      <th>Class</th>
      <th>Subject</th>
      <th>Teacher</th>
      <th>Room</th>
      <% if (userRole === 'admin') { %>
        <th>Actions</th>
      <% } %>
    </tr>
  </thead>
  <tbody>
    <% if (timetable && timetable.length > 0) { %>
      <% timetable.forEach(row => { %>
        <tr>
          <td><%= row.week_day %></td>
          <td><%= row.timetable_cycle %></td>
          <td><%= row.start_time %></td>
          <td><%= row.class_code || '-' %></td>
          <td><%= row.subject_name || '-' %></td>
          <td><%= (row.teacher_first_name && row.teacher_last_name) ? `${row.teacher_first_name} ${row.teacher_last_name}` : '-' %></td>
          <td><%= row.room %></td>

          <% if (userRole === 'admin') { %>
            <td>
              <!-- Botão de deletar -->
              <form action="/timetable/<%= row.timetable_id %>/delete" method="POST" style="display:inline;">
                <button type="submit" onclick="return confirm('Are you sure you want to delete this entry?')">Delete</button>
              </form>

              <!-- Botão de editar -->
              <a href="/timetable/<%= row.timetable_id %>/edit">
                <button type="button">Edit</button>
              </a>
            </td>
          <% } %>
        </tr>
      <% }) %>
    <% } else { %>
      <tr>
        <td colspan="<%= userRole === 'admin' ? 8 : 7 %>" style="text-align: center;">No timetable entries found.</td>
      </tr>
    <% } %>
  </tbody>
</table>
