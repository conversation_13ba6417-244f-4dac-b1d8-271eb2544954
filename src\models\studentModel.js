// src/models/studentModel.js
const db = require('../config/db');

const findStudentsByParentId = async (parentId) => {
  const { rows } = await db.query(
    'SELECT school_id, first_name, last_name FROM students WHERE person_ID = $1',
    [parentId]
  );
  return rows;
};

const findAllStudents = async () => {
  const { rows } = await db.query(
    'SELECT school_id, first_name, last_name FROM students'
  );
  return rows;
};

// ★ NOVO ★
const findStudentById = async (id) => {
  const { rows } = await db.query(
    'SELECT school_id, year_code, first_name, last_name FROM students WHERE school_id = $1',
    [id]
  );
  return rows[0];            // undefined se não achar
};

module.exports = {
  findStudentsByParentId,
  findAllStudents,
  findStudentById          // ← exporte!
};