const db = require('../config/db');

const studentModel = {
    async getAll() {
        const result = await db.query(`
            SELECT s.*, y.year_code
            FROM students s
            LEFT JOIN year y ON s.year_ID = y.year_ID
        `);
        return result.rows;
    },

    async getByParentId(parentId) {
        const result = await db.query(`
            SELECT s.*, y.year_code
            FROM students s
            LEFT JOIN year y ON s.year_ID = y.year_ID
            WHERE s.person_ID = $1
        `, [parentId]);
        return result.rows;
    },

    async getById(studentId) {
        const result = await db.query(`
            SELECT s.*, y.year_code, p.first_name as parent_first_name, p.last_name as parent_last_name
            FROM students s
            LEFT JOIN year y ON s.year_ID = y.year_ID
            LEFT JOIN parents p ON s.person_ID = p.person_ID
            WHERE s.School_ID = $1
        `, [studentId]);
        return result.rows[0];
    }
};

module.exports = studentModel;
