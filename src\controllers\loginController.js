const loginModel = require("../models/loginModel");
const bcrypt = require('bcryptjs');

// Renderiza a página de login
const renderLoginPage = (req, res) => {
  res.render('pages/login', {
    title: 'Login',
    error: null
  });
};

// Lida com o envio do formulário de login
const login = async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).render('pages/login', {
      title: 'Login',
      error: "Email e senha são obrigatórios."
    });
  }

  try {
    const user = await loginModel.findAnyUserByEmail(email);

    if (!user) {
      return res.status(401).render('pages/login', {
        title: 'Login',
        error: "Usuário não encontrado"
      });
    }

    const isValid = await bcrypt.compare(password, user.password);

    if (!isValid) {
      return res.status(401).render('pages/login', {
        title: 'Login',
        error: "Senha incorreta"
      });
    }
    
    req.session.userId = user.id;
    req.session.userRole = user.role;
    req.session.userName = `${user.first_name} ${user.last_name}`;

    console.log('Usuário autenticado:', user);
    
    // Login bem-sucedido: redireciona ou inicia sessão
    if(user.role === "admin"){
      return res.redirect('/home')
    } else {
      return res.redirect('/about'); // ou para a home/dashboard
    }

  } catch (error) {
    console.error("Erro ao fazer login:", error);
    return res.status(500).render('pages/login', {
      title: 'Login',
      error: "Erro interno do servidor. Tente novamente mais tarde."
    });
  }
};

module.exports = { renderLoginPage, login };
