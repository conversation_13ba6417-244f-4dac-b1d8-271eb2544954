<!-- src/views/pages/reports.ejs -->

<h1>Reports</h1>

<form method="get" action="/reports" class="reports-filter">
  <label for="year">Select Year:</label>
  <select name="year" id="year" onchange="this.form.submit()">
    <option value="">-- choose year --</option>
    <% years.forEach(y => { %>
      <option value="<%= y %>" <%= (y === selectedYear) ? 'selected' : '' %>>
        <%= y %>
      </option>
    <% }) %>
  </select>
</form>

<% if (selectedYear && reports.length === 0) { %>
  <p>No reports found for <strong><%= selectedYear %></strong>.</p>
<% } else if (reports.length > 0) { %>
  <div class="report-list">
    <% reports.forEach(r => { %>
      <div class="report-card">
        <!-- Left: PDF icon -->
        <div class="report-card__icon report-card__icon--left">
          <img src="/img/pdf-icon.svg" alt="PDF icon" width="24" height="24">
        </div>

        <!-- Middle: report title -->
        <div class="report-card__title">
          <%= selectedYear %> – <%= r.term %>
        </div>

        <!-- Right: download link -->
        <div class="report-card__icon report-card__icon--right">
          <a href="<%= r.pdf_path %>" download>
            <img src="/img/download-icon.svg" alt="Download report" width="24" height="24">
          </a>
        </div>
      </div>
    <% }) %>
  </div>
<% } %>
