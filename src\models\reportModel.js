// src/models/reportModel.js
const db = require('../config/db');

const reportModel = {
  async getByStudentId(studentId) {
    const result = await db.query(`
      SELECT *
      FROM reports
      WHERE School_ID = $1
      ORDER BY School_year DESC, Term DESC
    `, [studentId]);
    return result.rows;
  },

  async getById(reportId) {
    const result = await db.query(`
      SELECT r.*, s.first_name, s.last_name
      FROM reports r
      JOIN students s ON r.School_ID = s.School_ID
      WHERE r.report_ID = $1
    `, [reportId]);
    return result.rows[0];
  },

  // Adicionar a função findYearsByStudent
  async findYearsByStudent(studentId) {
    const result = await db.query(`
      SELECT DISTINCT school_year
      FROM reports
      WHERE School_ID = $1
      ORDER BY school_year DESC
    `, [studentId]);
    
    // Retorna um array apenas com os anos
    return result.rows.map(row => row.school_year);
  },

  // Adicionar a função findReportsByStudentYear
  async findReportsByStudentYear(studentId, year) {
    const result = await db.query(`
      SELECT *
      FROM reports
      WHERE School_ID = $1 AND school_year = $2
      ORDER BY term
    `, [studentId, year]);
    
    return result.rows;
  }
};

module.exports = reportModel;
