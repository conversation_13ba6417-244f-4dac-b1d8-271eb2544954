// src/models/reportModel.js
const db = require('../config/db');

const reportModel = {
  async findYearsByStudent(studentId) {
    const sql = `
      SELECT DISTINCT school_year
      FROM reports
      WHERE school_id = $1
      ORDER BY school_year DESC
    `;
    const { rows } = await db.query(sql, [studentId]);
    return rows.map(r => r.school_year);
  },

  async findReportsByStudentYear(studentId, year) {
    const sql = `
      SELECT report_id,
             term,
             pdf_path
      FROM reports
      WHERE school_id = $1
        AND school_year = $2
      ORDER BY term
    `;
    const { rows } = await db.query(sql, [studentId, year]);
    return rows;
  }
};

module.exports = reportModel;
