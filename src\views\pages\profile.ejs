<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/css/profile.css">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <main class="profile-content">
    <div class="container">
      <h2 class="section-title">Parent Personal Information</h2>

      <div class="parent-info-section">
        <form action="/profile/update" method="POST" class="parent-form" enctype="multipart/form-data">
          <div class="parent-info-grid">
            <!-- Primeira coluna -->
            <div class="info-column">
              <div class="form-group">
                <label for="first_name">First Name</label>
                <div class="input-wrapper">
                  <input type="text" id="first_name" name="first_name" value="<%= parentInfo.first_name %>" required>
                  <img src="/images/edit-icon.png" alt="Edit" class="edit-icon">
                </div>
              </div>

              <div class="form-group">
                <label for="last_name">Last Name</label>
                <div class="input-wrapper">
                  <input type="text" id="last_name" name="last_name" value="<%= parentInfo.last_name %>" required>
                  <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon">
                </div>
              </div>

              <div class="form-group">
                <label for="mobile">Phone Number</label>
                <div class="input-wrapper">
                  <input type="text" id="mobile" name="mobile" value="<%= parentInfo.mobile || '' %>">
                  <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon">
                </div>
              </div>
            </div>

            <!-- Segunda coluna -->
            <div class="info-column">
              <div class="form-group">
                <label for="email">Email</label>
                <div class="input-wrapper">
                  <input type="email" id="email" name="email" value="<%= parentInfo.email || '' %>" required>
                  <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon">
                </div>
              </div>

              <div class="form-group">
                <label for="adress_line">Address</label>
                <div class="input-wrapper">
                  <input type="text" id="adress_line" name="adress_line" value="<%= parentInfo.adress_line || '' %>">
                  <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon">
                </div>
              </div>

              <div class="form-group">
                <label for="post_code">Post Code</label>
                <div class="input-wrapper">
                  <input type="text" id="post_code" name="post_code" value="<%= parentInfo.post_code || '' %>">
                  <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon">
                </div>
              </div>
            </div>

            <!-- Terceira coluna - Foto do perfil -->
            <div class="profile-photo-column">
              <div class="profile-photo-wrapper">
                <% if (parentInfo.photo) { %>
                  <img src="<%= parentInfo.photo %>" alt="Profile Photo" class="profile-photo">
                <% } else { %>
                  <div class="profile-photo-placeholder">
                    <span>No Photo</span>
                  </div>
                <% } %>
                <div class="photo-edit-overlay">
                  <input type="file" id="parent-photo-upload" name="photo" accept="image/*" class="photo-upload-input">
                  <label for="parent-photo-upload" class="photo-edit-button">
                    <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon profile-edit-icon">
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div class="save-button-wrapper">
            <button type="submit" class="btn btn-save">Save</button>
          </div>
        </form>
      </div>

      <!-- Seção de informações médicas do estudante -->
      <div class="student-info-section">
        <h2 class="section-title">Student Personal Information</h2>

        <% if (studentInfo) { %>
          <div class="student-medical-box">
            <div class="student-layout">
              <!-- Foto do estudante à esquerda -->
              <div class="student-photo-section">
                <div class="student-photo-wrapper">
                  <% if (studentInfo.photo) { %>
                    <img src="<%= studentInfo.photo %>" alt="Student Photo" class="student-photo">
                  <% } else { %>
                    <div class="student-photo-placeholder">
                      <span>No Photo</span>
                    </div>
                  <% } %>
                  <div class="student-photo-edit-overlay">
                    <input type="file" id="student-photo-upload" name="student_photo" accept="image/*" class="photo-upload-input">
                    <label for="student-photo-upload" class="photo-edit-button">
                      <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon student-photo-edit-icon">
                    </label>
                  </div>
                </div>
              </div>

              <!-- Conteúdo à direita da foto -->
              <div class="student-name-and-input">
                <h3 class="student-name"><%= studentInfo.first_name %> <%= studentInfo.last_name %></h3>

                <form action="/profile/update" method="POST" class="student-form" id="student-medical-form" enctype="multipart/form-data">
                  <div class="medical-input-wrapper">
                    <textarea id="medical_notes" name="medical_notes" placeholder="Type any medical restrictions..." rows="3"><%= medicalInfo.medical_notes %></textarea>
                    <img src="/images/edit-icon.svg" alt="Edit" class="edit-icon medical-edit-icon">
                  </div>
                </form>
              </div>
            </div>

            <!-- Botão Save no canto inferior direito -->
            <div class="student-save-section">
              <button type="submit" form="student-medical-form" class="btn btn-save student-save">Save</button>
            </div>
          </div>
        <% } else { %>
          <div class="student-medical-box">
            <div class="alert alert-info">
              Select a student to view and edit medical information.
            </div>
          </div>
        <% } %>
      </div>
    </div>
  </main>

  <script>
    // Preview de imagem para foto do parent
    document.getElementById('parent-photo-upload').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const photoWrapper = document.querySelector('.profile-photo-wrapper');
          const existingImg = photoWrapper.querySelector('.profile-photo');
          const placeholder = photoWrapper.querySelector('.profile-photo-placeholder');

          if (existingImg) {
            existingImg.src = e.target.result;
          } else if (placeholder) {
            placeholder.innerHTML = `<img src="${e.target.result}" alt="Profile Photo" class="profile-photo">`;
          }
        };
        reader.readAsDataURL(file);
      }
    });

    // Preview de imagem para foto do student
    const studentPhotoUpload = document.getElementById('student-photo-upload');
    if (studentPhotoUpload) {
      studentPhotoUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(e) {
            const photoWrapper = document.querySelector('.student-photo-wrapper');
            const existingImg = photoWrapper.querySelector('.student-photo');
            const placeholder = photoWrapper.querySelector('.student-photo-placeholder');

            if (existingImg) {
              existingImg.src = e.target.result;
            } else if (placeholder) {
              placeholder.innerHTML = `<img src="${e.target.result}" alt="Student Photo" class="student-photo">`;
            }
          };
          reader.readAsDataURL(file);
        }
      });
    }
  </script>
</body>
</html>
