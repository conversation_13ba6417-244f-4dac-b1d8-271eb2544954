const homeModel = require('../models/homeModel');
const studentModel = require('../models/studentModel');
const db = require('../config/db');

// Renders the home page with events
const renderHomePage = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    const studentId = req.session.studentId;

    // Validate session
    if (!userId) {
      return res.redirect('/');
    }

    let yearId;
    let events = [];

    console.log('==== HOME PAGE DEBUG ====');
    console.log('User ID:', userId);
    console.log('User role:', userRole);
    console.log('User name:', userName);
    console.log('Student ID:', studentId);

    // Verificar diretamente se existem eventos no banco de dados
    try {
      const checkEvents = await db.query('SELECT COUNT(*) FROM events');
      console.log('Total events in database:', checkEvents.rows[0].count);
      
      // Listar todos os eventos para debug
      const allEventsDebug = await db.query('SELECT event_id, title, event_date FROM events');
      console.log('All events in database:');
      console.log(allEventsDebug.rows);
    } catch (dbError) {
      console.error('Error checking events table:', dbError);
    }

    // Get all events regardless of user role for testing
    try {
      const allEvents = await db.query('SELECT * FROM events ORDER BY event_date, time');
      events = allEvents.rows;
      console.log('Found events (direct query):', events.length);
      
      if (events.length > 0) {
        console.log('First event:', {
          id: events[0].event_id,
          title: events[0].title,
          date: events[0].event_date,
          time: events[0].time
        });
      }
    } catch (queryError) {
      console.error('Error in direct query:', queryError);
    }

    // Render the page with events
    console.log('Rendering home page with', events.length, 'events');
    return res.render('pages/home', {
      title: 'Home',
      events,
      userName,
      userRole,
      calendarPath: '/pdfs/calendar-2025.pdf'
    });

  } catch (error) {
    console.error("Error rendering home page:", error);
    return res.status(500).render('pages/error', {
      title: 'Error',
      error: "Internal server error. Please try again later."
    });
  }
};

module.exports = { renderHomePage };
