const homeModel = require('../models/homeModel');
const studentModel = require('../models/studentModel');
const db = require('../config/db');

// Renders the home page with events
const renderHomePage = async (req, res) => {
  try {
    const userId = req.session.userId;
    const userRole = req.session.userRole;
    const userName = req.session.userName;
    const studentId = req.session.studentId;

    // Validate session
    if (!userId) {
      return res.redirect('/');
    }

    let yearId;
    let events = [];

    // Get the year_ID based on the selected student or admin role
    if (userRole === 'parent' && studentId) {
      // Get student's year_ID
      const student = await studentModel.findStudentById(studentId);
      if (student && student.year_code) {
        // Get year_ID from year_code
        const yearResult = await db.query('SELECT year_ID FROM year WHERE year_code = $1', [student.year_code]);
        if (yearResult.rows.length > 0) {
          yearId = yearResult.rows[0].year_id;
        }
      }
    } else if (userRole === 'admin') {
      // Admin can see all events or filter by year
      // For simplicity, we'll show all events for admin
      const allEvents = await db.query('SELECT * FROM events ORDER BY event_date, time');
      events = allEvents.rows;
      return res.render('pages/home', {
        title: 'Home',
        events,
        userName,
        userRole,
        calendarPath: '/pdfs/calendar-2025.pdf'
      });
    }

    // If we have a year_ID, get events for that year
    if (yearId) {
      events = await homeModel.getEventsByYear(yearId);
    }

    return res.render('pages/home', {
      title: 'Home',
      events,
      userName,
      userRole,
      calendarPath: '/pdfs/calendar-2025.pdf'
    });

  } catch (error) {
    console.error("Error rendering home page:", error);
    return res.status(500).render('pages/error', {
      title: 'Error',
      error: "Internal server error. Please try again later."
    });
  }
};

module.exports = { renderHomePage };