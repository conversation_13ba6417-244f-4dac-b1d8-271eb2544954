const timetableModel = require('../models/timetableModel');
const db = require('../config/db');

const timetableController = {
    async getAll(req, res) {
        try {
            const userId = req.session.userId;
            const userRole = req.session.userRole;

            // Debug: Verificar a estrutura da tabela classes
            const tableStructure = await db.query(`
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'classes'
            `);
            console.log('Classes table structure:', tableStructure.rows);

            // Debug: Verificar os dados na tabela classes
            const classesDebug = await db.query('SELECT * FROM classes LIMIT 5');
            console.log('Classes table data:', classesDebug.rows);

            // Debug: Verificar a estrutura da tabela timetable
            const timetableStructure = await db.query(`
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'timetable'
            `);
            console.log('Timetable table structure:', timetableStructure.rows);

            // Verificar se os registros na tabela timetable têm class_ID válidos
            const classIDCheck = await db.query(`
                SELECT timetable_id, class_id 
                FROM timetable 
                LIMIT 10
            `);
            console.log('Timetable class_id check:', classIDCheck.rows);

            // Verificar se há correspondência entre os IDs
            const matchCheck = await db.query(`
                SELECT t.timetable_id, t.class_id, c.class_id as matched_class_id
                FROM timetable t
                LEFT JOIN classes c ON t.class_id = c.class_id
                LIMIT 10
            `);
            console.log('ID match check:', matchCheck.rows);

            let data;
            if (userRole === 'admin') {
                data = await timetableModel.getAll();
            } else if (userRole === 'parent') {
                data = await timetableModel.getAllForParent(userId);
            } else {
                return res.status(403).send('Acesso negado');
            }

            // Debug: Verificar os dados retornados pelo modelo
            console.log('Timetable data sample:', data.slice(0, 2));

            res.render('pages/timetable', { timetable: data, title: 'School timetable', userRole: userRole });
        } catch (error) {
            console.error('Timetable error:', error);
            res.status(500).send('Internal Server Error: ' + error.message);
        }
    },

    async getById(req, res) {
        try { 
            const userId = req.session.userId;
            const userRole = req.session.userRole;

            const { id } = req.params;
            const data = await timetableModel.getById(id);
            if (data) {
                res.json(data);
            } else {
                res.status(404).json({ message: 'Timetable entry not found' });
            }
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async create(req, res) {
        try {
            const userId = req.session.userId;
            const userRole = req.session.userRole;

            if (userRole !== 'admin') {
                return res.status(403).send('Apenas administradores podem criar horários');
            }

            const data = await timetableModel.create(req.body);
            res.status(201).redirect('/timetable');
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async update(req, res) {
        try {
            const userId = req.session.userId;
            const userRole = req.session.userRole;

            if (userRole !== 'admin') {
                return res.status(403).send('Apenas administradores podem editar horários');
            }

            const { id } = req.params;
            const data = await timetableModel.update(id, req.body);
            res.redirect(`/timetable`);
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async delete(req, res) {
        try {
            const userId = req.session.userId;
            const userRole = req.session.userRole;

            if (userRole !== 'admin') {
                return res.status(403).send('Apenas administradores podem deletar horários');
            }

            const { id } = req.params;
            await timetableModel.delete(id);
            res.redirect('/timetable');
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async renderEditForm(req, res) {
        try {
            const userId = req.session.userId;
            const userRole = req.session.userRole;

            if (userRole !== 'admin') {
                return res.status(403).send('Apenas administradores podem acessar esta página');
            }

            const { id } = req.params;
            const data = await timetableModel.getById(id);
            if (!data) {
                return res.status(404).send('Timetable not found');
            }
            res.render('pages/editTimetable', { timetable: data, title: 'Edit Timetable' });
        } catch (error) {
            res.status(500).send(error.message);
        }
    }
};

module.exports = timetableController;
