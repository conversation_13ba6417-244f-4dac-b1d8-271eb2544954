const timetableModel = require('../models/timetableModel');

const timetableController = {
    async getAll(req, res) {
        try {
            const data = await timetableModel.getAll();
            console.log('Resultado de getAll():', data); // ← Aqui
            res.render('pages/timetable', { timetable: data, title: 'School timetable' });
        } catch (error) {
            res.status(500).send('Internal Server Error: ' + error.message);
        }
    },

    async getById(req, res) {
        try { 
            const { id } = req.params;
            const data = await timetableModel.getById(id);
            if (data) {
                res.json(data);
            } else {
                res.status(404).json({ message: 'Timetable entry not found' });
            }
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async create(req, res) {
        try {
            const data = await timetableModel.create(req.body);
            res.status(201).redirect('/timetable');
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async update(req, res) {
        try {
            const { id } = req.params;
            console.log('Dados recebidos para update:', req.body);
            const data = await timetableModel.update(id, req.body);
            res.redirect(`/timetable`);
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async delete(req, res) {
        try {
            const { id } = req.params;
            const data = await timetableModel.delete(id);
            res.redirect('/timetable');
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    async renderEditForm(req, res) {
    try {
        const { id } = req.params;
        const data = await timetableModel.getById(id);
        if (!data) {
        return res.status(404).send('Timetable not found');
        }
        res.render('pages/editTimetable', { timetable: data, title: 'Edit Timetable' });
    } catch (error) {
        res.status(500).send(error.message);
    }
    }
};

module.exports = timetableController;
