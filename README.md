# Inteli - Instituto de Tecnologia e Liderança 

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="/assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# Nome do projeto

## Nome do grupo

## :student: Integrantes: 
- <a href="https://www.linkedin.com/in/adrianapolicia/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app">Adriana Policia</a>
- <a href="https://www.linkedin.com/"><PERSON></a>
- <a href="https://www.linkedin.com/in/francisco-filho-87b243346/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/guilhermeholandamarques/"><PERSON><PERSON><PERSON><PERSON></a> 
- <a href="https://www.linkedin.com/in/isaac-souza-santos/"><PERSON></a>
- <a href="https://www.linkedin.com/in/marcela-da-costa/"><PERSON>a <PERSON></a> 
- <a href="https://www.linkedin.com/in/yan-de-oliveira-ribeiro-0a55a3356/">Yan Ribeiro</a>

## :teacher: Professores:
### Orientador(a) 
- <a href="https://www.linkedin.com/in/juliastateri/">Julia Stateri</a>
### Instrutores
- <a href="https://www.linkedin.com/in/cristiano-benites-ph-d-687647a8/">Cristiano Benites</a>
- <a href="">Bruna Mayer</a> 
- <a href="https://www.linkedin.com/in/henrique-mohallem-paiva-6854b460/">Henrique Mohallem Paiva</a> 
- <a href="https://www.linkedin.com/in/renato-penha/">Renato Penha</a>
- <a href="https://www.linkedin.com/in/anacristinadossantos/">Ana Cristina dos Santos</a>

## 📝 Descrição

## Descrição do Projeto

O **Parents Portal** é uma aplicação web desenvolvida para a St. Paul’s School — uma das mais tradicionais escolas internacionais de São Paulo — com o objetivo de centralizar e facilitar o acesso às informações acadêmicas pelos responsáveis dos alunos. Até então, boa parte dos dados (boletins, horários, tarefas, eventos e informações médicas) estava disponível apenas em um app móvel restrito, resultando em uma experiência fragmentada e pouco intuitiva.

### Principais Funcionalidades

- **Login e autenticação**  
  Pais e administradores acessam o sistema com e-mail e senha.  
- **Seleção de aluno**  
  Permite que um responsável com múltiplos estudantes escolha qual perfil deseja visualizar em cada sessão.  
- **Relatórios**  
  Dropdown para “Select Year” e lista de boletins em PDF (1º, 2º, 3º termo etc.), com ícones de PDF à esquerda e botão de download à direita.  
- **Grade de aulas**  
  Exibição de horários semanais (ciclos “A” e “B”), sala, disciplina e nome do professor.  
- **Tarefas**  
  Listagem de assignments sincronizados via Microsoft Teams, separados por pendentes e concluídas.  
- **Informações médicas**  
  Registro e atualização de alergias, contatos de emergência e observações de saúde.  
- **Eventos e calendário**  
  Feed de eventos escolares e link para download do calendário acadêmico.

### Arquitetura e Tecnologias

- **Backend**: Node.js + Express  
- **Banco de Dados**: PostgreSQL (via `pg`)  
- **View Engine**: EJS (padrão MVC)  
- **Sessões**: `express-session` para manter o contexto de usuário e aluno selecionado  
- **Organização MVC**:  
  - **Models** tratam das queries SQL (p. ex. `reportModel`, `studentModel`).  
  - **Controllers** implementam a lógica de negócio e roteamento (p. ex. `reportsController`, `homeController`).  
  - **Views** renderizam templates EJS seguindo o layout principal.

### Benefícios Esperados

- **Centralização e clareza**: todas as informações acadêmicas em um único local, acessível por desktop e mobile.  
- **Melhor experiência do usuário**: interface intuitiva, sem necessidade de alternar entre múltiplos sistemas.  
- **Redução de carga administrativa**: diminuição de consultas diretas à secretaria e maior autonomia para os pais.  
- **Flexibilidade de expansão**: nova funcionalidade (perfil de professores, chat, notificações em tempo real) pode ser adicionada com facilidade.

Este projeto proporciona uma ponte digital moderna entre a St. Paul’s School e sua comunidade, alinhando tecnologia e pedagogia para elevar o nível de engajamento e satisfação de todos os envolvidos.


## 📝 Link de demonstração

_Coloque aqui o link para seu projeto publicado e link para vídeo de demonstração_

## 📁 Estrutura de pastas
Dentre os arquivos e pastas presentes na raiz do projeto, definem-se:

- **assets**: contém arquivos não estruturados do repositório, como imagens, ícones e demais recursos visuais.

- **documentos**: reúne os documentos do projeto, como o Web Application Document (WAD) e outros materiais complementares.

- **scripts**: scripts auxiliares para configuração e manipulação do banco de dados, como inicialização (`init.sql`), inserção de dados (`seed-data.js`) e execução automatizada.

- **src**: diretório com todo o código-fonte da aplicação web, organizado em subpastas como:
  - `config/`: arquivos de configuração da aplicação;
  - `controllers/`: lógica dos controladores que tratam as requisições;
  - `models/`: definição dos modelos de dados;
  - `routes/`: definição das rotas da aplicação;
  - `views/`: arquivos de visualização (EJS).

- **estude_no_inteli.txt**: arquivo de texto auxiliar incluído no projeto.

- **.gitignore**: define quais arquivos/pastas devem ser ignorados pelo Git.

- **`"README.md"`**: arquivo que serve como guia introdutório e explicação geral sobre o projeto (o mesmo arquivo que você está lendo agora).

- **app.js**: ponto de entrada da aplicação, onde o servidor é iniciado.

- **package.json** e **package-lock.json**: arquivos que gerenciam as dependências do projeto e suas versões.

```
├── assets/
├── documentos/
├── scripts/
│   ├── db-setup.js
│   ├── init.sql
│   ├── runSQLScript.js
│   └── seed-data.js
├── src/
│   ├── config/
│   ├── controllers/
│   ├── models/
│   ├── routes/
│   └── views/
├── estude_no_inteli.txt
├── .gitignore
├── README.md
├── app.js
├── package.json
└── package-lock.json
```


## 💻 Configuração para desenvolvimento e execução do código

*Acrescentar as informações necessárias sobre pré-requisitos (IDEs, bibliotecas, serviços etc.) e instalação básica do projeto, descrevendo eventuais versões utilizadas. Colocar um passo a passo de como o leitor pode baixar o código e executar a aplicação a partir de sua máquina local.*

*exemplo de instruções*

Aqui encontram-se todas as instruções necessárias para a instalação de todos os programas, bibliotecas e ferramentas imprescindíveis para a configuração do ambiente de desenvolvimento.

1. Baixar e instalar o node.js: [https://nodejs.org/pt-br/](https://nodejs.org/pt-br/) (versão 16.15.1 LTS)
2. Clone o repositório em questão.
3. No modo administrador, abra o "prompt de comando" ou o "terminal" e, após, abra a pasta "src/backend" no diretório raiz do repositório clonado e digite o segundo comando:

```sh
npm install
```

Isso instalará todas as dependências definidas no arquivo <b>package.json</b> que são necessárias para rodar o projeto. Agora o projeto já está pronto para ser modificado. 

4. Para criar o banco de dados, e inserir dados fictícios para teste, digite o seguinte comando no terminal:

```sh
npm run setup-db
```
5. Caso deseje criar apenas as tabelas, digite no terminal:

```sh
npm run init -db
```
Com isso, o banco de dados será criado sem nenhum dado armazenado.

6. Caso ainda deseje iniciar a aplicação, digite o comando abaixo no terminal:

```sh
npm start
```
7. Agora você pode acessar a aplicação através do link http://localhost:1234/
8. O servidor está online.

## 🗃 Histórico de lançamentos

* 0.5.0 - 30/04/2025
    * 
* 0.4.0 - 16/05/2025
    * 
* 0.3.0 - 30/05/2025
    * 
* 0.2.0 - 13/06/2025
    * 
* 0.1.0 - 26/06/2025
    *

## 📋 Licença/License
```
Alunos inteli (remover essa observação do readme.md após leitura e execução, junto com o link para o tutorial):

1. Siga o tutorial para criação da licença: https://drive.google.com/file/d/1hXWLHUhjBkPVuGqeE2LZKozFntnJZzlx/view
```

<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1"><img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1"><p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/"><a property="dct:title" rel="cc:attributionURL" href="https://github.com/Intelihub/Template_M2/">MODELO GIT INTELI</a> by <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.yggbrasil.com.br/vr">Inteli, Nome do integrante 1, Nome do integrante 2, Nome do integrante 3, Nome do integrante 4, Nome do integrante 5, Nome do integrante 6, Nome do integrante 7</a> is licensed under <a href="http://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Attribution 4.0 International</a>.</p>

